#ifndef __CSYUNJIROBOT_CONFIG_H__
#define __CSYUNJIROBOT_CONFIG_H__

#include <string>

// csvideorecord config struct
typedef struct YUNJI_ROBOT_CONFIG_T
{
    // http server port.
    int http_port;

    // HTTP handler thread number
    int http_thread_num = 10;

    // rpc server port.
    int rpc_port;

    // etcd server
    char etcd_server_addr[64];
    
    // 服务器内网ip
    char server_inner_ip[64];

    // DB配置项
    int  db_port;
    char db_ip[64];
    char db_username[64];
    char db_password[64];
    char db_database[64];

    // WebSocket配置项
    char websocket_url[256];               // WebSocket服务URL
    int websocket_heartbeat_interval;      // WebSocket心跳间隔（秒）
    char yunji_http_url[256];              // Http服务URL
} YUNJI_ROBOT_CONFIG;


#endif
