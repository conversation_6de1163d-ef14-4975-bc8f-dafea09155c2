#ifndef __CSYUNJIROBOT_HTTP_RESPONSE_H__
#define __CSYUNJIROBOT_HTTP_RESPONSE_H__

#include <cstdio>  
#include <fstream> 
#include <iostream>
#include <functional>
#include <evpp/http/context.h>

enum HTTP_ROUTE
{
    ON_METRICS = 0,
};

typedef std::function<void(const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)> HTTPRespCallback;
typedef std::map<std::string, HTTPRespCallback> HTTPRespVerCallbackMap;
typedef std::map<int, HTTPRespCallback> HTTPAllRespCallbackMap;

HTTPAllRespCallbackMap HTTPAllRespMapInit();

#endif