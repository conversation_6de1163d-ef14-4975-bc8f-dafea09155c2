#ifndef __CSYUNJIROBOT_NOTIFY_MERCHANT_CALL_MSG_H__
#define __CSYUNJIROBOT_NOTIFY_MERCHANT_CALL_MSG_H__

#include <string>
#include "NotifyMsg.h"
#include "AkcsCommonDef.h"
#include "NotifyMsgControl.h"

class NotifyMerchantCall : public CNotifyMsg
{
public:
    NotifyMerchantCall() {};
    ~NotifyMerchantCall() {}
    
    NotifyMerchantCall(const std::string& project_uuid, const std::string& task_id, const std::string& target, const std::string& via,
        int deposit_pin_code, int withdraw_pin_code)
    {
        project_uuid_ = project_uuid;
        task_id_ = task_id;
        target_ = target;
        via_ = via;
        deposit_pin_code_ = deposit_pin_code;
        withdraw_pin_code_ = withdraw_pin_code;
    }

    NotifyMerchantCall(const NotifyMerchantCall &that)
    {
        project_uuid_ = that.project_uuid_;
        task_id_ = that.task_id_;
        target_ = that.target_;
        via_ = that.via_;
        deposit_pin_code_ = that.deposit_pin_code_;
        withdraw_pin_code_ = that.withdraw_pin_code_;
    }
    
    NotifyMerchantCall(NotifyMerchantCall &&that)
    {
        project_uuid_ = std::move(that.project_uuid_);
        task_id_ = std::move(that.task_id_);
        target_ = std::move(that.target_);
        via_ = std::move(that.via_);
        deposit_pin_code_ = that.deposit_pin_code_;
        withdraw_pin_code_ = that.withdraw_pin_code_;
    }

    void NotifyMsg();
private:
    std::string project_uuid_;
    std::string task_id_;
    std::string target_;
    std::string via_;
    int deposit_pin_code_;
    int withdraw_pin_code_;
};

#endif