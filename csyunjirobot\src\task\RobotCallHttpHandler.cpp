#include "RobotCallHttpHandler.h"
#include <sstream>
#include <algorithm>
#include <chrono>
#include <memory>
#include <mutex>

// 全局HTTP处理器实例
static std::unique_ptr<RobotCallHttpHandler> g_http_handler;
static std::mutex g_handler_mutex;

RobotCallHttpHandler::RobotCallHttpHandler() : initialized_(false) {
    // 初始化API路由映射
    api_routes_[RobotCallRoutes::ROBOT_CALL] = "robot_call";
    api_routes_[RobotCallRoutes::TASK_STATUS] = "task_status";
    api_routes_[RobotCallRoutes::TASK_CANCEL] = "task_cancel";
    api_routes_[RobotCallRoutes::ROBOT_LIST] = "robot_list";
    api_routes_[RobotCallRoutes::SCHEDULER_STATUS] = "scheduler_status";
    api_routes_[RobotCallRoutes::HEALTH_CHECK] = "health_check";
}

RobotCallHttpHandler::~RobotCallHttpHandler() {

}