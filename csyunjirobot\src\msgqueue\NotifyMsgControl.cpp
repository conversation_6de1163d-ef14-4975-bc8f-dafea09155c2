#include "NotifyMsgControl.h"
#include "AkcsCommonDef.h"
#include "DclientMsgDef.h"

CNotifyMsgControl* CNotifyMsgControl::instance_ = NULL;

CNotifyMsgControl::~CNotifyMsgControl()
{
    notify_msg_list_.clear();
}

CNotifyMsgControl* GetNotifyMsgControlInstance()
{
    return CNotifyMsgControl::GetInstance();
}

CNotifyMsgControl* CNotifyMsgControl::GetInstance()
{
    if (instance_ == NULL)
    {
        instance_ = new CNotifyMsgControl();
    }
    return instance_;
}

int CNotifyMsgControl::Init()
{
    thread_ = std::thread(&CNotifyMsgControl::ProcessNotifyMsg, this);
    AK_LOG_INFO << "NotifyMsg Thread Start Success, thread_id = " << thread_.get_id();
    return 0;
}

int CNotifyMsgControl::GetNotifyMsgListSize()
{
    std::unique_lock<std::mutex> lock(mutex_);
    return notify_msg_list_.size();
}

int CNotifyMsgControl::ProcessNotifyMsg()
{
    while (1)
    {
        NotifyMsgPrt msg_ptr;
        {
            std::unique_lock<std::mutex> lock(mutex_);
            while (notify_msg_list_.size() == 0)
            {
                condition_variable_.wait(lock);
            }
           
            msg_ptr = notify_msg_list_.back();
            notify_msg_list_.pop_back();
        }
        
        msg_ptr->NotifyMsg();
    }
    return 0;
}

int CNotifyMsgControl::AddMerchantCallMsg(const NotifyMerchantCall&& msg)
{
    {
        std::unique_lock<std::mutex> lock(mutex_);
        notify_msg_list_.emplace_back(std::make_shared<NotifyMerchantCall>(std::move(msg)));
        condition_variable_.notify_all();
    }
    return 0;
}