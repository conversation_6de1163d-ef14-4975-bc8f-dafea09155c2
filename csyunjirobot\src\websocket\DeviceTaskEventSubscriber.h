#pragma once
#include <string>
#include <vector>
#include <functional>
#include <json/json.h>
#include "../task/RobotTaskStatus.h"

/**
 * 设备任务事件数据结构
 * 对应 subscribe_device_task_events 返回的数据
 */
struct DeviceTaskEvent {
    std::string action;                 // 机器人执行的动作名称
    std::string action_name;            // 机器人执行的动作名称中文描述
    std::string timestamp;              // 机器人上报消息时间戳
    std::string device_id;              // 执行任务的设备 ID
    std::string target;                 // 当前机器人目标点
    std::string task_flow_id;           // 任务流程 ID
    std::string task_flow_name;         // 任务流程名称
    std::string task_id;                // 当前机器人执行任务 ID
    std::string robot_task_id;          // 当前机器人本体任务 ID
    std::string move_task_id;           // 当前机器人移动任务 ID
    std::string task_channel;           // 任务类型渠道(可选)
    std::string out_task_id;            // 三方任务 ID(可选)
    std::string option_id;              // 任务流程节点 ID(可选)
    
    /**
     * 从 JSON 数据解析任务事件
     * @param json JSON 数据
     */
    void FromJson(const Json::Value& json) {
        action = json.get("action", "").asString();
        action_name = json.get("action_name", "").asString();
        timestamp = json.get("timestamp", "").asString();
        device_id = json.get("device_id", "").asString();
        target = json.get("target", "").asString();
        task_flow_id = json.get("task_flow_id", "").asString();
        task_flow_name = json.get("task_flow_name", "").asString();
        task_id = json.get("task_id", "").asString();
        robot_task_id = json.get("robot_task_id", "").asString();
        move_task_id = json.get("move_task_id", "").asString();
        task_channel = json.get("task_channel", "").asString();
        out_task_id = json.get("out_task_id", "").asString();
        option_id = json.get("optionId", "").asString();
    }
    
    /**
     * 转换为 JSON 数据
     * @param json 输出的 JSON 对象
     */
    void ToJson(Json::Value& json) const {
        json["action"] = action;
        json["action_name"] = action_name;
        json["timestamp"] = timestamp;
        json["device_id"] = device_id;
        json["target"] = target;
        json["task_flow_id"] = task_flow_id;
        json["task_flow_name"] = task_flow_name;
        json["task_id"] = task_id;
        json["robot_task_id"] = robot_task_id;
        json["move_task_id"] = move_task_id;
        json["task_channel"] = task_channel;
        json["out_task_id"] = out_task_id;
        json["optionId"] = option_id;
    }
    
    /**
     * 判断是否为特定设备的事件
     * @param deviceId 设备ID
     * @return true表示是该设备的事件
     */
    bool IsDeviceEvent(const std::string& deviceId) const {
        return device_id == deviceId;
    }
    
    /**
     * 判断是否为特定任务的事件
     * @param taskId 任务ID
     * @return true表示是该任务的事件
     */
    bool IsTaskEvent(const std::string& taskId) const {
        return task_id == taskId;
    }
    
    /**
     * 获取事件的字符串表示（用于日志）
     * @return 事件描述字符串
     */
    std::string ToString() const {
        return "DeviceTaskEvent{device_id=" + device_id + 
               ", task_id=" + task_id + 
               ", action=" + action + 
               ", action_name=" + action_name + 
               ", target=" + target + "}";
    }
};

/**
 * 设备任务事件订阅器
 * 处理 subscribe_device_task_events 的订阅和事件回调
 */
class DeviceTaskEventSubscriber {
public:
    // 任务事件回调函数类型
    using TaskEventCallback = std::function<void(const DeviceTaskEvent& event)>;
    
    /**
     * 构造函数
     */
    DeviceTaskEventSubscriber() = default;
    
    /**
     * 析构函数
     */
    ~DeviceTaskEventSubscriber() = default;
    
    /**
     * 设置任务事件回调函数
     * @param callback 回调函数
     */
    void SetTaskEventCallback(const TaskEventCallback& callback) {
        task_event_callback_ = callback;
    }
    
    /**
     * 处理接收到的任务事件消息
     * @param json 接收到的 JSON 消息
     */
    void HandleTaskEventMessage(const Json::Value& json) {
        if (!json.isMember("data")) {
            return;
        }
        
        DeviceTaskEvent event;
        event.FromJson(json["data"]);
        
        // 触发回调
        if (task_event_callback_) {
            task_event_callback_(event);
        }
    }
    
    /**
     * 生成订阅设备任务事件的请求消息
     * @param devices 设备ID列表
     * @param cid 客户端ID
     * @param timestamp 时间戳
     * @return JSON 格式的订阅请求
     */
    static Json::Value CreateSubscribeRequest(const std::vector<std::string>& devices, 
                                            const std::string& cid, 
                                            int64_t timestamp) {
        Json::Value request;
        request["cmd"] = "subscribe_device_task_events";
        request["cid"] = cid;
        request["timestamp"] = static_cast<Json::Int>(timestamp);
        
        Json::Value data;
        Json::Value deviceArray(Json::arrayValue);
        for (const auto& device : devices) {
            deviceArray.append(device);
        }
        data["devices"] = deviceArray;
        request["data"] = data;
        
        return request;
    }
    
    /**
     * 检查消息是否为任务事件响应
     * @param json 接收到的消息
     * @return true表示是任务事件响应
     */
    static bool IsTaskEventResponse(const Json::Value& json) {
        if (!json.isMember("cmd") || !json.isMember("respToCmd")) {
            return false;
        }
        
        std::string cmd = json["cmd"].asString();
        std::string respToCmd = json["respToCmd"].asString();
        
        return cmd == "result" && respToCmd == "subscribe_device_task_events";
    }
    
private:
    TaskEventCallback task_event_callback_;     // 任务事件回调函数
};