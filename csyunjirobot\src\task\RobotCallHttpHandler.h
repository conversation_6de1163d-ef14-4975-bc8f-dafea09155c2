/**
 * 机器人召唤HTTP接口处理器
 * 
 * 处理来自客户端的机器人召唤HTTP请求
 */

#ifndef __ROBOT_CALL_HTTP_HANDLER_H__
#define __ROBOT_CALL_HTTP_HANDLER_H__

#include <string>
#include <map>
#include <functional>
#include "AkLogging.h"

/**
 * HTTP请求信息
 */
struct HttpRequestInfo {
    std::string method;                             // 请求方法 GET/POST/PUT/DELETE
    std::string path;                               // 请求路径
    std::string query_string;                       // 查询字符串
    std::map<std::string, std::string> headers;     // 请求头
    std::string body;                               // 请求体
    std::string client_ip;                          // 客户端IP
    
    HttpRequestInfo() = default;
};

/**
 * HTTP响应信息
 */
struct HttpResponseInfo {
    int status_code;                                // 状态码
    std::string status_message;                     // 状态消息
    std::map<std::string, std::string> headers;     // 响应头
    std::string body;                               // 响应体
    
    HttpResponseInfo() : status_code(200), status_message("OK") {
        headers["Content-Type"] = "application/json; charset=utf-8";
        headers["Server"] = "csyunjirobot/1.0";
    }
};

/**
 * HTTP接口路由枚举
 */
enum class RobotCallRoutes {
    ROBOT_CALL,         // 机器人召唤
    TASK_STATUS,        // 任务状态查询
    TASK_CANCEL,        // 任务取消
    ROBOT_LIST,         // 机器人列表
    SCHEDULER_STATUS,   // 调度器状态
    HEALTH_CHECK        // 健康检查
};

/**
 * 机器人召唤HTTP接口处理器
 */
class RobotCallHttpHandler {
public:
    /**
     * 构造函数
     */
    RobotCallHttpHandler();

    /**
     * 析构函数
     */
    ~RobotCallHttpHandler();

private:
    bool initialized_;                                          // 初始化标志
    std::map<RobotCallRoutes, std::string> api_routes_;        // API路由映射
};



#endif // __ROBOT_CALL_HTTP_HANDLER_H__
