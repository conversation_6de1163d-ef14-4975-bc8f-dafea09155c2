#include "util_judge.h"
#include "VideoStorageConfig.h"

VideoStorageConfigHandle::VideoStorageConfigHandle(const std::string& project_uuid)
{
    dbinterface::VideoStorage::GetVideoStorageListByAccountUUID(project_uuid, video_storage_info_list_);
    dbinterface::VideoStorageDevice::GetVideoStorageDevicesListByProjectUUID(project_uuid, video_storage_dev_list_);
    InitVideoStorageInfoMap();
    InitVideoStorageDevicesList();
}

// 初始化视频存储信息map，key是社区uuid或者personalAccountUUID，value是VideoStorageInfo
void VideoStorageConfigHandle::InitVideoStorageInfoMap()
{
    for (const auto& video_storage_info : video_storage_info_list_)
    {
        if (video_storage_info.belong_type == VideoStorageBelongType::VIDEO_STORAGE_BELONG_TYPE_PUB
        || video_storage_info.belong_type == VideoStorageBelongType::VIDEO_STORAGE_BELONG_TYPE_UNIT )
        {
            video_storage_info_map_[video_storage_info.account_uuid] = video_storage_info;
        }
        else
        {
            video_storage_info_map_[video_storage_info.personal_account_uuid] = video_storage_info;
        }
    }
}

// 获取设备视频存储信息
bool VideoStorageConfigHandle::GetVideoStorageInfo(const DEVICE_SETTING* dev, VideoStorageInfo& video_storage_info)
{
    if (akjudge::IsCommunityPublicDev(dev->grade))
    {
        if (video_storage_info_map_.find(dev->project_uuid) != video_storage_info_map_.end())
        {
            video_storage_info = video_storage_info_map_[dev->project_uuid];
            return true;
        }
    }
    else
    {
        if (video_storage_info_map_.find(dev->node_uuid) != video_storage_info_map_.end())
        {
            video_storage_info = video_storage_info_map_[dev->node_uuid];
            return true;
        }
    }
    return false;
}

// 初始化视频存储设备列表
void VideoStorageConfigHandle::InitVideoStorageDevicesList()
{
    for (const auto& video_storage_dev : video_storage_dev_list_)
    {
        video_storage_devices_uuid_list_.insert(video_storage_dev.devices_uuid);
    }
}

bool VideoStorageConfigHandle::IsVideoStorageDevices(const std::string& dev_uuid)
{
    return video_storage_devices_uuid_list_.find(dev_uuid) != video_storage_devices_uuid_list_.end();
}