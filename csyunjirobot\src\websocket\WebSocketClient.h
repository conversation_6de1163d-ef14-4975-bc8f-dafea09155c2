#ifndef __CSYUNJIROBOT_WEB_SOCKET_CLIENT_H__
#define __CSYUNJIROBOT_WEB_SOCKET_CLIENT_H__

#include <string>
#include <vector>
#include <thread>
#include <chrono>
#include "IXWebSocket.h"
#include "IXWebSocketHttpHeaders.h"

/**
 * 云际Robot WebSocket客户端类
 *
 * 提供与云际Robot
 *  WebSocket服务的连接、认证、消息发送和接收功能
 * 支持自动心跳、JSON消息格式、错误处理等特性
 */
class YunJiRobotWebSocketClient {

public:
    YunJiRobotWebSocketClient(const std::string& url,
                          const std::string& key_id = "",
                          const std::string& key_secret = "");
    ~YunJiRobotWebSocketClient();

    void AuthenticatedUrl();

    void Connect();

    void Disconnect();

    void SendTextMessage(const std::string& message);

    void SendRequest(const std::string& action, const std::vector<std::string>& devices, const std::string& cid = "");

    void SendPing();

    void StartHeartbeat(int intervalSeconds = 30);

    bool GetIsConnected() const;

    void SubscribeRobotPosition(const std::vector<std::string>& devices, const std::string& cid = "");

    void SubscribeDeviceStatus(const std::vector<std::string>& devices, const std::string& cid = "");

    void SubscribeDeviceTaskEvents(const std::vector<std::string>& devices, const std::string& cid = "");

private:
    void HandleMessage(const ix::WebSocketMessagePtr& msg);

    void ProcessMessage(const std::string& message);

    void HandleRobotTaskResponse(const std::string& response);

    void OnConnected();

    void OnDisconnected();

private:
    ix::WebSocket web_socket_;              // WebSocket连接对象
    std::string wss_url_;                   // WebSocket服务URL
    std::string access_key_id_;             // 访问密钥ID
    std::string access_key_secret_;         // 访问密钥Secret   
    std::string authenticated_url_;         // 认证后的完整URL
    bool is_connected_;                     // 连接状态标志
    bool enable_heartbeat_;                 // 心跳启用标志
    std::thread heartbeat_thread_;          // 心跳线程
};


#endif // __CSYUNJI_WEB_SOCKET_CLIENT_H__