#pragma once
#include <string>
#include <vector>
#include <memory>
#include <functional>
#include <unordered_map>
#include <mutex>
#include <thread>
#include <atomic>
#include <chrono>
#include <json/json.h>
#include "../task/RobotTaskStatus.h"
#include "DeviceTaskEventSubscriber.h"
#include "HeartbeatManager.h"
#include "WebSocketClient.h"

// 前向声明
class YunJiRobotWebSocketClient;

/**
 * WebSocket客户端信息结构
 */
struct WebSocketClientInfo {
    ::std::shared_ptr<YunJiRobotWebSocketClient> client;     // 客户端实例
    ::std::string client_id;                              // 客户端唯一标识
    ::std::string url;                                    // 连接URL
    ::std::string access_key_id;                          // 访问密钥ID
    ::std::string access_key_secret;                      // 访问密钥Secret
    bool is_active;                                       // 是否活跃
    ::std::chrono::steady_clock::time_point last_heartbeat; // 最后心跳时间

    WebSocketClientInfo() : is_active(false) {}
};

/**
 * 云际AI WebSocket 管理器
 * 管理多个WebSocket客户端连接，提供统一的心跳管理和连接监控
 */
class YunJiWebSocketManager {
public:
    // 连接状态变化回调类型
    using ConnectionStateCallback = std::function<void(const std::string& client_id, bool connected)>;

    // 客户端错误回调类型
    using ClientErrorCallback = std::function<void(const std::string& client_id, const std::string& error)>;

    /**
     * 构造函数
     * @param heartbeat_interval 心跳间隔（秒），默认30秒
     */
    explicit YunJiWebSocketManager(int heartbeat_interval = 30)
        : heartbeat_interval_(heartbeat_interval)
        , is_running_(false) {
    }
    
    /**
     * 析构函数
     */
    ~YunJiWebSocketManager() {
        Stop();
    }

    /**
     * 启动管理器
     * 开始心跳监控线程
     */
    void Start() {
        if (is_running_) {
            return;
        }

        is_running_ = true;
        heartbeat_thread_ = std::thread(&YunJiWebSocketManager::HeartbeatLoop, this);
    }

    /**
     * 停止管理器
     * 断开所有连接并停止心跳监控
     */
    void Stop() {
        is_running_ = false;

        if (heartbeat_thread_.joinable()) {
            heartbeat_thread_.join();
        }

        DisconnectAllClients();
    }

    /**
     * 添加WebSocket客户端
     * @param client_id 客户端唯一标识
     * @param url WebSocket服务URL
     * @param access_key_id 访问密钥ID
     * @param access_key_secret 访问密钥Secret
     * @return 添加成功返回true
     */
    bool AddClient(const std::string& client_id,
                   const std::string& url,
                   const std::string& access_key_id = "",
                   const std::string& access_key_secret = "") {
        std::lock_guard<std::mutex> lock(clients_mutex_);

        if (clients_.find(client_id) != clients_.end()) {
            return false; // 客户端已存在
        }

        auto client_info = std::make_shared<WebSocketClientInfo>();
        client_info->client = std::make_shared<YunJiRobotWebSocketClient>(url, access_key_id, access_key_secret);
        client_info->client_id = client_id;
        client_info->url = url;
        client_info->access_key_id = access_key_id;
        client_info->access_key_secret = access_key_secret;
        client_info->is_active = false;
        client_info->last_heartbeat = std::chrono::steady_clock::now();

        clients_[client_id] = client_info;
        return true;
    }

    /**
     * 移除WebSocket客户端
     * @param client_id 客户端标识
     * @return 移除成功返回true
     */
    bool RemoveClient(const std::string& client_id) {
        std::lock_guard<std::mutex> lock(clients_mutex_);

        auto it = clients_.find(client_id);
        if (it == clients_.end()) {
            return false;
        }

        // 断开连接
        if (it->second->client) {
            it->second->client->Disconnect();
        }

        clients_.erase(it);
        return true;
    }

    /**
     * 连接指定客户端
     * @param client_id 客户端标识
     * @return 连接成功返回true
     */
    bool ConnectClient(const std::string& client_id) {
        std::lock_guard<std::mutex> lock(clients_mutex_);

        auto it = clients_.find(client_id);
        if (it == clients_.end()) {
            return false;
        }

        if (it->second->client) {
            it->second->client->Connect();
            it->second->is_active = true;
            it->second->last_heartbeat = std::chrono::steady_clock::now();

            // 触发连接状态回调
            if (connection_state_callback_) {
                connection_state_callback_(client_id, true);
            }
        }

        return true;
    }

    /**
     * 断开指定客户端
     * @param client_id 客户端标识
     * @return 断开成功返回true
     */
    bool DisconnectClient(const std::string& client_id) {
        std::lock_guard<std::mutex> lock(clients_mutex_);

        auto it = clients_.find(client_id);
        if (it == clients_.end()) {
            return false;
        }

        if (it->second->client) {
            it->second->client->Disconnect();
            it->second->is_active = false;

            // 触发连接状态回调
            if (connection_state_callback_) {
                connection_state_callback_(client_id, false);
            }
        }

        return true;
    }

    /**
     * 连接所有客户端
     */
    void ConnectAllClients() {
        std::lock_guard<std::mutex> lock(clients_mutex_);

        for (auto& pair : clients_) {
            if (pair.second->client && !pair.second->is_active) {
                pair.second->client->Connect();
                pair.second->is_active = true;
                pair.second->last_heartbeat = std::chrono::steady_clock::now();

                // 触发连接状态回调
                if (connection_state_callback_) {
                    connection_state_callback_(pair.first, true);
                }
            }
        }
    }

    /**
     * 断开所有客户端
     */
    void DisconnectAllClients() {
        std::lock_guard<std::mutex> lock(clients_mutex_);

        for (auto& pair : clients_) {
            if (pair.second->client && pair.second->is_active) {
                pair.second->client->Disconnect();
                pair.second->is_active = false;

                // 触发连接状态回调
                if (connection_state_callback_) {
                    connection_state_callback_(pair.first, false);
                }
            }
        }
    }

    /**
     * 获取客户端实例
     * @param client_id 客户端标识
     * @return 客户端实例指针，如果不存在返回nullptr
     */
    std::shared_ptr<YunJiRobotWebSocketClient> GetClient(const std::string& client_id) {
        std::lock_guard<std::mutex> lock(clients_mutex_);

        auto it = clients_.find(client_id);
        if (it != clients_.end()) {
            return it->second->client;
        }

        return nullptr;
    }

    /**
     * 获取所有客户端ID列表
     * @return 客户端ID列表
     */
    std::vector<std::string> GetAllClientIds() const {
        std::lock_guard<std::mutex> lock(clients_mutex_);

        std::vector<std::string> client_ids;
        for (const auto& pair : clients_) {
            client_ids.push_back(pair.first);
        }

        return client_ids;
    }

    /**
     * 获取活跃客户端数量
     * @return 活跃客户端数量
     */
    size_t GetActiveClientCount() const {
        std::lock_guard<std::mutex> lock(clients_mutex_);

        size_t count = 0;
        for (const auto& pair : clients_) {
            if (pair.second->is_active) {
                count++;
            }
        }

        return count;
    }

    /**
     * 检查客户端是否连接
     * @param client_id 客户端标识
     * @return 连接状态
     */
    bool IsClientConnected(const std::string& client_id) const {
        std::lock_guard<std::mutex> lock(clients_mutex_);

        auto it = clients_.find(client_id);
        if (it != clients_.end()) {
            return it->second->is_active && it->second->client && it->second->client->GetIsConnected();
        }

        return false;
    }

    /**
     * 设置心跳间隔
     * @param interval_seconds 心跳间隔（秒）
     */
    void SetHeartbeatInterval(int interval_seconds) {
        heartbeat_interval_ = interval_seconds;
    }

    /**
     * 设置连接状态变化回调
     * @param callback 回调函数
     */
    void SetConnectionStateCallback(const ConnectionStateCallback& callback) {
        connection_state_callback_ = callback;
    }

    /**
     * 设置客户端错误回调
     * @param callback 回调函数
     */
    void SetClientErrorCallback(const ClientErrorCallback& callback) {
        client_error_callback_ = callback;
    }

    /**
     * 获取管理器状态信息
     * @return JSON格式的状态信息
     */
    std::string GetStatusInfo() const;

private:
    /**
     * 心跳循环线程函数
     * 定期检查所有客户端的连接状态并发送心跳
     */
    void HeartbeatLoop() {
        while (is_running_) {
            CheckAndSendHeartbeats();

            // 等待心跳间隔时间
            std::this_thread::sleep_for(std::chrono::seconds(heartbeat_interval_));
        }
    }

    /**
     * 检查并发送心跳
     * 遍历所有活跃客户端，发送心跳并检查连接状态
     */
    void CheckAndSendHeartbeats() {
        std::lock_guard<std::mutex> lock(clients_mutex_);

        auto now = std::chrono::steady_clock::now();

        for (auto& pair : clients_) {
            auto& client_info = pair.second;

            if (!client_info->is_active || !client_info->client) {
                continue;
            }

            // 检查客户端是否真正连接
            if (!client_info->client->GetIsConnected()) {
                client_info->is_active = false;

                // 触发连接状态回调
                if (connection_state_callback_) {
                    connection_state_callback_(pair.first, false);
                }
                continue;
            }

            // 发送心跳
            try {
                client_info->client->SendPing();
                client_info->last_heartbeat = now;
            } catch (const std::exception& e) {
                // 心跳发送失败，标记为非活跃
                client_info->is_active = false;

                // 触发错误回调
                if (client_error_callback_) {
                    client_error_callback_(pair.first, std::string("Heartbeat failed: ") + e.what());
                }

                // 触发连接状态回调
                if (connection_state_callback_) {
                    connection_state_callback_(pair.first, false);
                }
            }
        }
    }

private:
    // 客户端管理
    std::unordered_map<std::string, std::shared_ptr<WebSocketClientInfo>> clients_;
    mutable std::mutex clients_mutex_;

    // 心跳管理
    int heartbeat_interval_;                    // 心跳间隔（秒）
    std::atomic<bool> is_running_;              // 运行状态
    std::thread heartbeat_thread_;              // 心跳线程

    // 回调函数
    ConnectionStateCallback connection_state_callback_;
    ClientErrorCallback client_error_callback_;
};