#include "EtcdCliMng.h"
#include "ResidRpcClientInit.h"
#include "VideoRecordClient.h"
#include "VideoRecordClientMng.h"
#include "cspbx_rpc_client.h"
#include "cspbx_rpc_client_mng.h"
#include "YunJiRobotClient.h"
#include "YunJiRobotClientMng.h"

extern CAkEtcdCliManager* g_etcd_cli_mng;

void GrpcClientInit()
{
    VideoRecordGrpcClientInit();
    PbxRpcClientInit();
    YunJiRobotGrpcClientInit();
    return;
}

void YunJiRobotGrpcClientInit()
{
    std::set<std::string> csyunjirrobot_grpc_server_addrs;
    if (g_etcd_cli_mng->GetAllYunJiRobotRpcSrvs(csyunjirrobot_grpc_server_addrs) != 0)
    {
        AK_LOG_FATAL << "YunJiRobot GrpcClientInit connetc to etcd srv fialed";
        return;
    }
    
    for (const auto& csyunjirrobot_grpc_server_addr : csyunjirrobot_grpc_server_addrs)
    {
        auto csyunjirobot_rpc_client = std::make_shared<VideoRecordRpcClient>(csyunjirrobot_grpc_server_addr);
        YunJiRobotRpcClientMng::Instance()->AddYunJiRobotRpcSrv(csyunjirrobot_grpc_server_addr, csyunjirobot_rpc_client);
    }

    return; 
}

void VideoRecordGrpcClientInit()
{
    std::set<std::string> csvideo_grpc_server_addrs;
    if (g_etcd_cli_mng->GetAllVideoRecordRpcSrvs(csvideo_grpc_server_addrs) != 0)
    {
        AK_LOG_FATAL << "VideorRecord GrpcClientInit connetc to etcd srv fialed";
        return;
    }
    
    for (const auto& csvido_record_grpc_addr : csvideo_grpc_server_addrs)
    {
        auto csvideorecord_rpc_client = std::make_shared<VideoRecordRpcClient>(csvido_record_grpc_addr);
        VideoRecordClientMng::Instance()->AddVideoRecordRpcSrv(csvido_record_grpc_addr, csvideorecord_rpc_client);
    }

    return;
}

void PbxRpcClientInit()
{
    std::set<std::string> pbx_grpc_server_addrs;
    if (g_etcd_cli_mng->GetAllPbxRpcInnerSrvs(pbx_grpc_server_addrs) != 0)
    {
        AK_LOG_FATAL << "PbxRpcClientInit connetc to etcd srv fialed";
        return;
    }
    
    for (const auto& pbx_grpc_addr : pbx_grpc_server_addrs)
    {
        auto pbx_rpc_client = std::make_shared<PbxRpcClient>(pbx_grpc_addr);
        PbxRpcClientMng::Instance()->AddPbxRpcSrv(pbx_grpc_addr, pbx_rpc_client);
    }

    return;
}