#include "SafeCacheConn.h"
#include "AkcsCommonDef.h"
#include "AkcsHttpRequest.h"
#include "YunJiRobotConfig.h"
#include "YunJiRobotApi.hpp"
#include "YunJiRobotApiUtil.hpp"
#include "NotifyMerchantCall.h"
#include "dbinterface/YunJiRobotInfo.h"

extern YUNJI_ROBOT_CONFIG g_yunji_robot_config;

void NotifyMerchantCall::NotifyMsg()
{
    CommunityYunJiRobotInfo community_yunji_info;
    if (0 != dbinterface::YunJiRobotInfo::GetYunJiRobotInfoByAccountUUID(project_uuid_, community_yunji_info))
    {  
        AK_LOG_INFO << "get yunjirobot info failed, project_uuid= " << project_uuid_;
        return;
    }

    YunJiAccessTokenData access_token_data;
    if (!YunJiRobotApi::GetAccessToken(community_yunji_info.access_key_id, community_yunji_info.access_key_secret, access_token_data))
    {
        return;
    }

    MerchantCallResponse response_data;
    if (!YunJiRobotApi::RequestRobotMerchantCall(access_token_data, task_id_, target_, via_, response_data))
    {
        return;
    }
    AK_LOG_INFO << "RequestRobotMerchantCall success, taskId: " << response_data.task_id;

    SafeCacheConn cache_conn(g_redis_db_yunji_robot);
    cache_conn.set(task_id_, response_data.task_id);

    return;
} 