#!/bin/bash
ACMD="$1"
csyunjirobot_BIN='/usr/local/akcs/csyunjirobot/bin/csyunjirobot'

if [ "$#" -ne 1 ];then
     echo "Usage: sh `basename $0` start|stop|restart|status"
     exit
fi

start_csyunjirobot()
{
	nohup $csyunjirobot_BIN >/dev/null 2>&1 &
    echo "Start csyunjirobot successful"
    if [ -z "`ps -fe|grep "csyunjirobotrun.sh" |grep -v grep`" ];then
        nohup bash /usr/local/akcs/csyunjirobot/scripts/csyunjirobotrun.sh >/dev/null 2>&1 &
    fi
}
stop_csyunjirobot()
{
    echo "Begin to stop csyunjirobotrun.sh"
    csyunjirobotrunid=`ps aux | grep -w csyunjirobotrun.sh | grep -v grep | awk '{ print $(2) }'`
    if [ -n "${csyunjirobotrunid}" ];then
	    echo "csyunjirobotrun.sh is running at ${csyunjirobotrunid}, will kill it first."
	    kill -9 ${csyunjirobotrunid}
    fi
    echo "Begin to stop csyunjirobot"
    kill -9 `pidof csyunjirobot`
    sleep 2
    echo "Stop csyunjirobot successful"
}

case $ACMD in
  start)
     start_csyunjirobot
    ;;
  stop)
     stop_csyunjirobot
    ;;
  restart)
    stop_csyunjirobot
    sleep 1
    start_csyunjirobot
    ;;
  status)
    if [ -f /var/run/csyunjirobot.pid ];then
        pid=`cat /var/run/csyunjirobot.pid`
        if [ $pid"x" = "x" ];then
           #pid里面的文件是空的
           pid="xxxxxxxxxx"
        fi
    else
        #重启之后没有这个pid文件
        pid="xxxxxxxxxx"
    fi
    cnt=`ls /proc/$pid | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        echo "\033[1;31;05m csyunjirobot is stop!!!\033[0m"
        exit 1
    else
        echo "\033[0;32m csyunjirobot is running \033[0m"
        exit 0
    fi
    ;;
  *)
    echo "Usage: sh `basename $0` start|stop|restart|status"
    ;;
esac
exit

