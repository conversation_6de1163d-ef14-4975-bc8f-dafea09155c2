#include "Md5.h"
#include "json/json.h"
#include "AkLogging.h"
#include "util_string.h"
#include "HttpMessage.h"
#include "HttpHandler.h"
#include "SafeCacheConn.h"
#include "AkcsCommonDef.h"
#include "AkcsHttpRequest.h"
#include "YunJiRobotConfig.h"
#include "YunJiRobotErrorCode.h"
#include "MetricService.h"

extern YUNJI_ROBOT_CONFIG g_yunji_robot_config;

HTTPRespCallback OnMetricsHandler = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    try
    {
        AK_LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
        MetricService* metric_service = MetricService::GetInstance();
        if (metric_service == nullptr)
        {
            AK_LOG_WARN << "metric service init failed.";
            cb("# metric service init failed.");
            return;
        }
    
        metric_service->UpdateMetrics();
        std::string response = metric_service->ToFormateString();
        cb(response);
    }
    catch(const std::exception& e)
    {
        AK_LOG_WARN << "OnMetricsHandler catch exception: " << e.what();
        cb(BuildHttpResponseMessage(ERR_CODE_SERVER_ERR));
    }
};

HTTPAllRespCallbackMap HTTPAllRespMapInit()
{
    HTTPAllRespCallbackMap OMap;
    OMap[HTTP_ROUTE::ON_METRICS] = OnMetricsHandler;
    return OMap;
}