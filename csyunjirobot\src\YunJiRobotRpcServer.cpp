#include "util.h"
#include "YunJiRobotRpcServer.h"
#include "NotifyMerchantCall.h"

void YunJiRobotRpcServer::Run()
{
    std::string listen_net = GetEth0IPAddr() + std::string(":") + rpc_port_;
    std::string server_address(listen_net);
    ServerBuilder builder;
    builder.AddListeningPort(server_address, grpc::InsecureServerCredentials());
    builder.RegisterService(&service_); 
    cq_ = builder.AddCompletionQueue();//可以多个的.
    server_ = builder.BuildAndStart();
    AK_LOG_INFO << "YunJiRobotRpcServer run grpc server listening on " << server_address;

    new CallData(&service_, cq_.get(), CSYUNJIROBOT_RPC_SERVER_TYPE::MERCHANT_CALL);

    std::thread HandleRpcsThread(std::bind(&YunJiRobotRpcServer::HandleRpcs, this));
    HandleRpcs();
    return;
}

void YunJiRobotRpcServer::CallData::Proceed()
{
    if (status_ == CREATE)
    {
        // Make this instance progress to the PROCESS state.
        status_ = PROCESS;
        switch (s_type_)
        {
            case CSYUNJIROBOT_RPC_SERVER_TYPE::MERCHANT_CALL:
            {
                service_->RequestRobotMerchantCallHandle(&ctx_, &robot_merchant_call_request_, &robot_merchant_call_responder_, cq_, cq_, this);
                break;
            }
            default:
            {
                AK_LOG_WARN << "invalid service type " << (int)s_type_;
                break;
            }
        }
    }
    else if (status_ == PROCESS)
    {
        status_ = FINISH;
        new CallData(service_, cq_, this->s_type_);
        switch (s_type_)
        {
            case CSYUNJIROBOT_RPC_SERVER_TYPE::MERCHANT_CALL:
            {
                NotifyMerchantCall notify_merchant_call(robot_merchant_call_request_.project_uuid(), robot_merchant_call_request_.task_id(), 
                    robot_merchant_call_request_.target(), robot_merchant_call_request_.via(), 
                    robot_merchant_call_request_.deposit_pin_code(), robot_merchant_call_request_.withdraw_pin_code());

                GetNotifyMsgControlInstance()->AddMerchantCallMsg(std::move(notify_merchant_call));
                
                robot_merchant_call_reply_.set_ret(0);
                robot_merchant_call_responder_.Finish(robot_merchant_call_reply_, Status::OK, this);
                break;
            }
            default:
            {
                AK_LOG_WARN << "invalid service type " << (int)s_type_;
                break;
            }
        }
    }
    else
    {
        GPR_ASSERT(status_ == FINISH);
        // Once in the FINISH state, deallocate ourselves (CallData).
        delete this;
    }
}

// This can be run in multiple threads if needed.
void YunJiRobotRpcServer::HandleRpcs()
{
    //TODO 当开启多线程的时候,这个必须挪到业务线程之前?
    //new CallData(&service_, cq_.get());
    void* tag;
    bool ok;
    while (true)
    {
        {
            std::lock_guard<std::mutex> lock(mtx_cq_);
            //modified by chenyc,2021-10-19,原先的代码写得不严谨,在一些场景下ok可能为false,具体可参考:gRPC源码中CompletionQueue的描述
            //TODO: 每个HandleRpcs线程单独一个cq,避免加锁与消息干扰.
            if(cq_->Next(&tag, &ok) != true || !ok)
            {
                AK_LOG_WARN << "gRPC HandleRpcs cq next operation error ";
                continue;
            }
            //GPR_ASSERT(cq_->Next(&tag, &ok));
            //GPR_ASSERT(ok);//这里断言会概率失败
        }
        static_cast<CallData*>(tag)->Proceed();
    }
}

