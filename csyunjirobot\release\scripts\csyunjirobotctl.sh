#!/bin/bash
ACMD="$1"
PWD="/usr/local/akcs/csyunjirobot/scripts"

if [ "$#" -ne 1 ];then
     echo "Usage: sh `basename $0` start|stop|restart|status"
     exit
fi

start_akcs()
{   
    sh ${PWD}/csyunjirobot.sh start
}
stop_akcs()
{
    sh ${PWD}/csyunjirobot.sh stop
}

status_akcs()
{
    sh ${PWD}/csyunjirobot.sh status
}

uninstall_akcs()
{
    sh ${PWD}/csyunjirobot.sh stop
    kill -9 `ps aux | grep csyunjirobotrun.sh |grep -v grep | awk '{print $2}'`
    rm -rf /usr/local/akcs/csyunjirobot/
}

case $ACMD in
  start)
        start_akcs
    ;;
  stop)
        stop_akcs
    ;;
  uninstall)
        uninstall_akcs
    ;;
  restart)
    stop_akcs
    sleep 1
    start_akcs
    ;;    
  status)
    status_akcs
    ;;  
  *)  
    echo "Usage: sh `basename $0` start|stop|restart|status|uninstall"
    ;;
esac
exit

