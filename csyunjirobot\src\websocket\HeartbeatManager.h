#pragma once
#include <string>
#include <thread>
#include <atomic>
#include <chrono>
#include <functional>
#include <mutex>
#include <json/json.h>

/**
 * 心跳管理器
 * 负责 WebSocket 连接的心跳保持 (ping/pong)
 * 根据云际AI API文档：默认超时时间30s，两个心跳周期内收不到服务器的心跳包，客户端可判定连接已断开
 */
class HeartbeatManager {
public:
    // 心跳发送回调函数类型
    using SendMessageCallback = std::function<void(const std::string& message)>;
    
    // 连接断开回调函数类型
    using ConnectionLostCallback = std::function<void()>;
    
    /**
     * 构造函数
     * @param sendCallback 发送消息的回调函数
     * @param lostCallback 连接断开的回调函数
     */
    HeartbeatManager(const SendMessageCallback& sendCallback, 
                    const ConnectionLostCallback& lostCallback)
        : send_callback_(sendCallback)
        , connection_lost_callback_(lostCallback)
        , is_running_(false)
        , heartbeat_interval_(30)  // 默认30秒心跳间隔
        , timeout_threshold_(60)   // 默认60秒超时阈值（两个心跳周期）
        , last_pong_time_(std::chrono::steady_clock::now()) {
    }
    
    /**
     * 析构函数
     */
    ~HeartbeatManager() {
        Stop();
    }
    
    /**
     * 启动心跳管理
     * @param intervalSeconds 心跳间隔（秒），默认30秒
     */
    void Start(int intervalSeconds = 30) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        if (is_running_) {
            return;
        }
        
        heartbeat_interval_ = intervalSeconds;
        timeout_threshold_ = intervalSeconds * 2;  // 两个心跳周期
        is_running_ = true;
        last_pong_time_ = std::chrono::steady_clock::now();
        
        // 启动心跳线程
        heartbeat_thread_ = std::thread(&HeartbeatManager::HeartbeatLoop, this);
    }
    
    /**
     * 停止心跳管理
     */
    void Stop() {
        {
            std::lock_guard<std::mutex> lock(mutex_);
            is_running_ = false;
        }
        
        if (heartbeat_thread_.joinable()) {
            heartbeat_thread_.join();
        }
    }
    
    /**
     * 处理接收到的 pong 消息
     * @param json 接收到的 pong 消息
     */
    void HandlePongMessage(const Json::Value& json) {
        if (!json.isMember("cmd") || json["cmd"].asString() != "pong") {
            return;
        }
        
        std::lock_guard<std::mutex> lock(mutex_);
        last_pong_time_ = std::chrono::steady_clock::now();
        
        // 可以在这里记录 pong 的时间戳用于延迟计算
        if (json.isMember("timestamp")) {
            int64_t server_timestamp = json["timestamp"].asInt();
            // 这里可以计算网络延迟等
            (void)server_timestamp; // 避免未使用变量警告
        }
    }
    
    /**
     * 手动发送一次心跳
     */
    void SendPing() {
        if (!send_callback_) {
            return;
        }
        
        Json::Value ping_message;
        ping_message["cmd"] = "ping";
        ping_message["timestamp"] = static_cast<Json::Int>(GetCurrentTimestamp());
        
        Json::FastWriter writer;
        std::string message = writer.write(ping_message);
        
        // 移除末尾换行符
        if (!message.empty() && message.back() == '\n') {
            message.pop_back();
        }
        
        send_callback_(message);
    }
    
    /**
     * 检查消息是否为 pong 响应
     * @param json 接收到的消息
     * @return true表示是 pong 响应
     */
    static bool IsPongMessage(const Json::Value& json) {
        return json.isMember("cmd") && json["cmd"].asString() == "pong";
    }
    
    /**
     * 创建 ping 消息
     * @param timestamp 时间戳
     * @return JSON 格式的 ping 消息
     */
    static Json::Value CreatePingMessage(int64_t timestamp) {
        Json::Value ping;
        ping["cmd"] = "ping";
        ping["timestamp"] = static_cast<Json::Int>(timestamp);
        return ping;
    }
    
    /**
     * 获取心跳间隔
     * @return 心跳间隔（秒）
     */
    int GetHeartbeatInterval() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return heartbeat_interval_;
    }
    
    /**
     * 设置心跳间隔
     * @param intervalSeconds 心跳间隔（秒）
     */
    void SetHeartbeatInterval(int intervalSeconds) {
        std::lock_guard<std::mutex> lock(mutex_);
        heartbeat_interval_ = intervalSeconds;
        timeout_threshold_ = intervalSeconds * 2;
    }
    
    /**
     * 检查连接是否超时
     * @return true表示连接超时
     */
    bool IsConnectionTimeout() const {
        std::lock_guard<std::mutex> lock(mutex_);
        auto now = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - last_pong_time_).count();
        return elapsed > timeout_threshold_;
    }
    
    /**
     * 获取距离上次 pong 的时间（秒）
     * @return 距离上次 pong 的秒数
     */
    int GetSecondsSinceLastPong() const {
        std::lock_guard<std::mutex> lock(mutex_);
        auto now = std::chrono::steady_clock::now();
        return static_cast<int>(std::chrono::duration_cast<std::chrono::seconds>(now - last_pong_time_).count());
    }

private:
    /**
     * 心跳循环线程函数
     */
    void HeartbeatLoop() {
        while (is_running_) {
            // 发送心跳
            SendPing();
            
            // 等待心跳间隔
            std::this_thread::sleep_for(std::chrono::seconds(heartbeat_interval_));
            
            // 检查连接超时
            if (IsConnectionTimeout()) {
                // 连接超时，触发断开回调
                if (connection_lost_callback_) {
                    connection_lost_callback_();
                }
                break;
            }
        }
    }
    
    /**
     * 获取当前时间戳（毫秒）
     * @return 当前时间戳
     */
    static int64_t GetCurrentTimestamp() {
        auto now = std::chrono::system_clock::now();
        auto timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch());
        return timestamp.count();
    }

private:
    SendMessageCallback send_callback_;                     // 发送消息回调
    ConnectionLostCallback connection_lost_callback_;       // 连接断开回调
    
    std::atomic<bool> is_running_;                         // 是否正在运行
    std::thread heartbeat_thread_;                         // 心跳线程
    
    mutable std::mutex mutex_;                             // 互斥锁
    int heartbeat_interval_;                               // 心跳间隔（秒）
    int timeout_threshold_;                                // 超时阈值（秒）
    std::chrono::steady_clock::time_point last_pong_time_; // 上次收到 pong 的时间
};