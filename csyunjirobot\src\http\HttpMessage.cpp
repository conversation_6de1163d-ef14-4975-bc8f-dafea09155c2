#include "json/json.h"
#include "HttpMessage.h"
#include "AkLogging.h"

#define CODE                     "code"
#define MESSAGE                  "message"
#define DATA                     "data"
#define ERROR                    "error"
#define PATH                     "path"
#define SECOND                    "second"

std::string GetErrorMessage(const std::string& code)
{
    auto it = g_error_code_message.find(code);
    if (it != g_error_code_message.end())
    {
        return it->second;
    }

    return "";
}

std::string BuildHttpErrorMessage(const std::string& code)
{
    Json::Value item;
    Json::FastWriter w;

    item[CODE] = code;
    item[MESSAGE] = GetErrorMessage(code);

    std::string msg_json = w.write(item);
    return msg_json;
}

std::string BuildHttpResponseHookMessage(int code, const std::string& error, const std::string& path, int second)
{
    Json::Value item;
    Json::FastWriter writer;

    item[CODE] = code;
    if (!error.empty())
    {   
        item[ERROR] = error;
    }

    if (!path.empty())
    {   
       item[PATH] = path;
    }
    
    if (second)
    {   
        item[SECOND] = second;
    }

    std::string msg_json = writer.write(item);
    AK_LOG_INFO  << "msg ret = " << msg_json;
    return msg_json;
}

std::string BuildHttpResponseMessage(const std::string& code, const HttpRespKV& kv)
{
    Json::Value item;
    Json::FastWriter w;
    Json::Value itemData;
    Json::FastWriter wData;

    item[CODE] = code;
    item[MESSAGE] = GetErrorMessage(code);
    for (const auto& tmpkv : kv)
    {
        itemData[tmpkv.first] = tmpkv.second;
    }
    item[DATA] = itemData;

    std::string msg_json = w.write(item);

    return msg_json;
}
