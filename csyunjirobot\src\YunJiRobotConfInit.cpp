#include "util.h"
#include "EtcdCliMng.h"
#include "ConnectionPool.h"
#include "ConfigFileReader.h"
#include "LogConnectionPoolManage.h"
#include "MappingConnectionPool.h"
#include "YunJiRobotConfInit.h"
#include "YunJiRobotDefine.h"
#include "YunJiRobotConfig.h"
#include "YunJiRobotRpcServer.h"
#include "dbinterface/Log/LogSlice.h"
#include "dbinterface/SystemSettingTable.h"
#include <KdcDecrypt.h>

extern CAkEtcdCliManager* g_etcd_cli_mng;
extern YUNJI_ROBOT_CONFIG g_yunji_robot_config;

void ConfigInit()
{
    CConfigFileReader config_file(PROCESS_CONF_FILE);

    g_yunji_robot_config.db_port = ATOI(config_file.GetConfigName("akcs_db_port"));
    g_yunji_robot_config.rpc_port = ATOI(config_file.GetConfigName("rpc_port"));
    g_yunji_robot_config.http_port = ATOI(config_file.GetConfigName("http_port"));
    g_yunji_robot_config.http_thread_num = ATOI(config_file.GetConfigName("http_thread_num"));

    Snprintf(g_yunji_robot_config.db_ip, sizeof(g_yunji_robot_config.db_ip),  config_file.GetConfigName("akcs_db_ip"));

    //获取数据库密码
    CConfigFileReader kdc_config_file("/etc/kdc.conf");
    const char* encrypt_db_passwd = kdc_config_file.GetConfigName("AKCS_DBUSER01");
    std::string decrypt_db_passwd = akuvox_encrypt::KdcDecrypt(std::string(encrypt_db_passwd));
    Snprintf(g_yunji_robot_config.db_password, sizeof(g_yunji_robot_config.db_password), decrypt_db_passwd.c_str());

    Snprintf(g_yunji_robot_config.db_username, sizeof(g_yunji_robot_config.db_username),  config_file.GetConfigName("db_username"));
    Snprintf(g_yunji_robot_config.db_database, sizeof(g_yunji_robot_config.db_database),  config_file.GetConfigName("akcs_db_database"));
    Snprintf(g_yunji_robot_config.etcd_server_addr, sizeof(g_yunji_robot_config.etcd_server_addr), config_file.GetConfigName("etcd_srv_net"));
    Snprintf(g_yunji_robot_config.server_inner_ip, sizeof(g_yunji_robot_config.server_inner_ip), config_file.GetConfigName("server_inner_ip"));

    // 读取WebSocket配置
    Snprintf(g_yunji_robot_config.websocket_url, sizeof(g_yunji_robot_config.websocket_url), config_file.GetConfigName("websocket_url"));
    Snprintf(g_yunji_robot_config.yunji_http_url, sizeof(g_yunji_robot_config.yunji_http_url), config_file.GetConfigName("yunji_http_url"));
    g_yunji_robot_config.websocket_heartbeat_interval = ATOI(config_file.GetConfigName("websocket_heartbeat_interval"));

    return;
}

int LoadConfFromConfSrv()
{
    SrvDbConf conf_tmp;
    memset(&conf_tmp, 0, sizeof(conf_tmp));
    g_etcd_cli_mng->LoadSrvDbOuterConf(conf_tmp);
    if((strlen(conf_tmp.db_ip) == 0) || (conf_tmp.db_port == 0))
    {
        return -1;
    }
    Snprintf(g_yunji_robot_config.db_ip, sizeof(g_yunji_robot_config.db_ip),  conf_tmp.db_ip);
    g_yunji_robot_config.db_port = conf_tmp.db_port;
    return 0;
}

int DaoInit()
{
    ConnPool* gConnPool = GetDBConnPollInstance();
    if (NULL == gConnPool)
    {
        AK_LOG_WARN << "DaoInit failed.";
        return -1;
    }
    LoadConfFromConfSrv();
    gConnPool->Init(g_yunji_robot_config.db_ip, g_yunji_robot_config.db_username, g_yunji_robot_config.db_password, g_yunji_robot_config.db_database, g_yunji_robot_config.db_port, MAX_RLDB_CONN, "csvideorecord");
    return 0;
}

int DaoReInit()
{
    ConnPool* conn_pool = GetDBConnPollInstance();
    if (NULL == conn_pool)
    {
        AK_LOG_WARN << "DaoReInit failed.";
        return -1;
    }
    conn_pool->ReInit(g_yunji_robot_config.db_ip, g_yunji_robot_config.db_port);
    return 0;
}

int RpcServerInit()
{
    YunJiRobotRpcServer rpc_server("8817");
    rpc_server.Run();
    return 0;
}
