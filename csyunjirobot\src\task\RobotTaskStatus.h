#ifndef __CSYUNJIROBOT_TASK_STATUS_H__
#define __CSYUNJIROBOT_TASK_STATUS_H__

#include <string>
#include <unordered_map>
#include <functional>
#include "AkcsCommonDef.h"

/*
供机器人任务状态与字符串之间的转换功能

class RobotTaskStatusHelper {
public:
    static std::string ToString(RobotTaskStatus status) {
        static const std::unordered_map<RobotTaskStatus, std::string> statusMap = {
            {RobotTaskStatus::UNKNOWN, "UNKNOWN"},
            {RobotTaskStatus::ON_THE_WAY, "ON_THE_WAY"},
            {RobotTaskStatus::PENDING, "PENDING"},
            {RobotTaskStatus::ARRIVED, "ARRIVED"},
            {RobotTaskStatus::DELIVERING, "DELIVERING"},
            {RobotTaskStatus::PLACEMENT_TIMEOUT, "PLACEMENT_TIMEOUT"},
            {RobotTaskStatus::PLACEMENT_CANCELLED, "PLACEMENT_CANCELLED"},
            {RobotTaskStatus::DELIVERY_ARRIVED, "DELIVERY_ARRIVED"},
            {RobotTaskStatus::WAITING_PICKUP, "WAITING_PICKUP"},
            {RobotTaskStatus::PICKUP_TIMEOUT, "PICKUP_TIMEOUT"},
            {RobotTaskStatus::DELIVERY_CANCELLED, "DELIVERY_CANCELLED"},
            {RobotTaskStatus::PICKUP_COMPLETED, "PICKUP_COMPLETED"}
        };

        auto it = statusMap.find(status);
        return (it != statusMap.end()) ? it->second : "UNKNOWN";
    }

    static RobotTaskStatus FromString(const std::string& statusStr) {
        static const std::unordered_map<std::string, RobotTaskStatus> stringMap = {
            {"UNKNOWN", RobotTaskStatus::UNKNOWN},
            {"ON_THE_WAY", RobotTaskStatus::ON_THE_WAY},
            {"PENDING", RobotTaskStatus::PENDING},
            {"ARRIVED", RobotTaskStatus::ARRIVED},
            {"DELIVERING", RobotTaskStatus::DELIVERING},
            {"PLACEMENT_TIMEOUT", RobotTaskStatus::PLACEMENT_TIMEOUT},
            {"PLACEMENT_CANCELLED", RobotTaskStatus::PLACEMENT_CANCELLED},
            {"DELIVERY_ARRIVED", RobotTaskStatus::DELIVERY_ARRIVED},
            {"WAITING_PICKUP", RobotTaskStatus::WAITING_PICKUP},
            {"PICKUP_TIMEOUT", RobotTaskStatus::PICKUP_TIMEOUT},
            {"DELIVERY_CANCELLED", RobotTaskStatus::DELIVERY_CANCELLED},
            {"PICKUP_COMPLETED", RobotTaskStatus::PICKUP_COMPLETED}
        };

        auto it = stringMap.find(statusStr);
        return (it != stringMap.end()) ? it->second : RobotTaskStatus::UNKNOWN;
    }
    
    static std::string GetDescription(RobotTaskStatus status) {
        static const std::unordered_map<RobotTaskStatus, std::string> descriptionMap = {
            {RobotTaskStatus::UNKNOWN, "未知状态"},
            {RobotTaskStatus::ON_THE_WAY, "机器人正在赶来"},
            {RobotTaskStatus::PENDING, "无空闲机器人"},
            {RobotTaskStatus::ARRIVED, "机器人到达门口"},
            {RobotTaskStatus::DELIVERING, "送物中"},
            {RobotTaskStatus::PLACEMENT_TIMEOUT, "放物超时"},
            {RobotTaskStatus::PLACEMENT_CANCELLED, "取消放物"},
            {RobotTaskStatus::DELIVERY_ARRIVED, "送物到达"},
            {RobotTaskStatus::WAITING_PICKUP, "等待取物"},
            {RobotTaskStatus::PICKUP_TIMEOUT, "取物超时"},
            {RobotTaskStatus::DELIVERY_CANCELLED, "送物取消"},
            {RobotTaskStatus::PICKUP_COMPLETED, "取物完成"}
        };

        auto it = descriptionMap.find(status);
        return (it != descriptionMap.end()) ? it->second : "未知状态";
    }
};

// 为RobotTaskStatus枚举提供std::hash特化
namespace std {
    template<>
    struct hash<RobotTaskStatus> {
        std::size_t operator()(const RobotTaskStatus& status) const {
            return std::hash<int>()(static_cast<int>(status));
        }
    };
}

*/
#endif