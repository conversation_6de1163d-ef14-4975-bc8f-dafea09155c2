#include <sstream>
#include <string.h>
#include <algorithm>
#include "Rldb.h"
#include "util.h"
#include "util_judge.h"
#include "AkLogging.h"
#include "RldbQuery.h"
#include "AKCSMsg.h"
#include "util_time.h"
#include "AkcsCommonDef.h"
#include "ConnectionManager.h"
#include "dbinterface/Account.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"
#include "dbinterface/resident/PersonalAccountUserInfo.h"
#include "dbinterface/InterfaceComm.h"
#include "dbinterface/CommunityInfo.h"
#include "dbinterface/DataConfusion.h"
#include "dbinterface/PmAccountMap.h"
#include "dbinterface/AccountUserInfo.h"
#include "dbinterface/PersonalAccountCnf.h"
#include "dbinterface/mapping/PhoneMapping.h"
#include "dbinterface/DistributorInfo.h"
#include "util_time.h"


namespace dbinterface
{

ResidentPersonalAccount::ResidentPersonalAccount()
{

}

// encrypt field : Name、FirstName、LastName、Phone、Phone2、Phone3

static const std::string account_sec = " P.ID, P.Name,P.ParentID,P.Account,P.UnitID,P.EnableIpDirect,P.UUID, \
P.Phone,P.PhoneCode,P.SipAccount,P.Active,P.ExpireTime < now() as isExpire,P.Role,P.Initialization,P.TempKeyPermission,P.Language,P.EnableSmartHome, \
P.ParentUUID,P.PhoneExpireTime < now() as isPhoneExpire,unix_timestamp(P.ExpireTime),unix_timestamp(P.PhoneExpireTime),P.TimeZone,P.ExpireTime,P.PhoneExpireTime,P.RoomNumber,unix_timestamp(NOW()), \
P.ReadMsgID,P.UserInfoUUID,P.RoomID,P.Special,P.PhoneStatus,P.Phone2,P.Phone3,P.NFCCode,P.BLECode,P.FirstName,P.LastName,P.Switch,P.CommunityUnitUUID,P.appLoginStatus,P.Ringtone,P.CommunityRoomUUID,P.EnableStrongAlarm ";




static csmain::DeviceType ChangeRoleToConnType(int role)
{
    if (role == ACCOUNT_ROLE_COMMUNITY_MAIN
        || role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT
        || role == ACCOUNT_ROLE_COMMUNITY_PM)
    {
        return csmain::DeviceType::COMMUNITY_APP;
    }
    else if (IsOfficeRole(role))
    {
        return csmain::DeviceType::OFFICE_APP;
    }
    else
    {
        return csmain::DeviceType::PERSONNAL_APP;
    }
}

void ResidentPersonalAccount::GetAccountFromSql(ResidentPerAccount &account, CRldbQuery& query)
{
    const int AKCS_LAST_SQL_INDEX = 42;//这个值要和最后下标一致    
    account.id = ATOI(query.GetRowData(0));
    Snprintf(account.name, sizeof(account.name), dbinterface::DataConfusion::Decrypt(query.GetRowData(1)).c_str());
    account.parent_id = ATOI(query.GetRowData(2));
    Snprintf(account.account, sizeof(account.account), query.GetRowData(3));
    account.unit_id = ATOI(query.GetRowData(4));
    account.ip_direct = ATOI(query.GetRowData(5));
    Snprintf(account.uuid, sizeof(account.uuid), query.GetRowData(6));
    Snprintf(account.phone, sizeof(account.phone), dbinterface::DataConfusion::Decrypt(query.GetRowData(7)).c_str());
    Snprintf(account.phone_code, sizeof(account.phone_code), query.GetRowData(8));
    snprintf(account.phone_with_phonecode, sizeof(account.phone_with_phonecode), "%s%s", account.phone_code, account.phone);
    Snprintf(account.sip_account, sizeof(account.sip_account), query.GetRowData(9));
    account.active = ATOI(query.GetRowData(10));
    account.is_expire = ATOI(query.GetRowData(11));
    account.role = ATOI(query.GetRowData(12));
    account.is_init = ATOI(query.GetRowData(13));
    account.is_show_tmpkey = ATOI(query.GetRowData(14));
    Snprintf(account.language, sizeof(account.language), query.GetRowData(15));
    account.enable_smarthome = ATOI(query.GetRowData(16));
    Snprintf(account.parent_uuid, sizeof(account.parent_uuid), query.GetRowData(17));
    account.is_feature_expire = ATOI(query.GetRowData(18));
    account.exp_stamp = ATOI(query.GetRowData(19));
    account.phone_exp_stamp = ATOI(query.GetRowData(20));
    Snprintf(account.timezone, sizeof(account.timezone), query.GetRowData(21));
    Snprintf(account.expire_time, sizeof(account.expire_time), query.GetRowData(22));
    Snprintf(account.phone_expire_time, sizeof(account.phone_expire_time), query.GetRowData(23));
    Snprintf(account.room_number, sizeof(account.room_number), query.GetRowData(24));
    account.cur_stamp = ATOI(query.GetRowData(25));
    account.lastread_message_id = ATOI(query.GetRowData(26));
    Snprintf(account.user_info_uuid, sizeof(account.user_info_uuid), query.GetRowData(27));
    account.room_id = ATOI(query.GetRowData(28));
    account.only_apt = ATOI(query.GetRowData(29));
    account.phone_status = ATOI(query.GetRowData(30));
    Snprintf(account.phone2, sizeof(account.phone2), dbinterface::DataConfusion::Decrypt(query.GetRowData(31)).c_str());
    Snprintf(account.phone3, sizeof(account.phone3), dbinterface::DataConfusion::Decrypt(query.GetRowData(32)).c_str());
    Snprintf(account.nfc_code, sizeof(account.nfc_code), query.GetRowData(33));
    Snprintf(account.ble_code, sizeof(account.ble_code), query.GetRowData(34));
    Snprintf(account.firstname, sizeof(account.firstname), dbinterface::DataConfusion::Decrypt(query.GetRowData(35)).c_str());
    Snprintf(account.lastname, sizeof(account.lastname), dbinterface::DataConfusion::Decrypt(query.GetRowData(36)).c_str());
    account.switch_value = ATOI(query.GetRowData(37));
    Snprintf(account.unit_uuid, sizeof(account.unit_uuid), query.GetRowData(38));    
    account.app_login_status = ATOI(query.GetRowData(39));
    Snprintf(account.ringtone, sizeof(account.ringtone), query.GetRowData(40));
    Snprintf(account.community_room_uuid, sizeof(account.community_room_uuid), query.GetRowData(41));
    account.strong_alarm = ATOI(query.GetRowData(AKCS_LAST_SQL_INDEX));

    // 其他业务逻辑的赋值
    account.conn_type = ChangeRoleToConnType(account.role);

    return;
}


int ResidentPersonalAccount::InitAccountByUid(const std::string& uid, ResidentPerAccount &account)
{    
    std::stringstream sql;
    sql << "select "<< account_sec <<" from PersonalAccount P where Account = '" << uid << "'";

    int ret = -1;
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    if (query.MoveToNextRow())
    {
        GetAccountFromSql(account, query);
        ret = 0;
    }
    ReleaseDBConn(conn);

    return ret;    
}

int ResidentPersonalAccount::InitAccountByUidFromMasterDB(const std::string& uid, ResidentPerAccount &account)
{
    std::stringstream sql;
    sql << "/*master*/select "<< account_sec <<" from PersonalAccount P where Account = '" << uid << "'";

    int ret = -1;
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    if (query.MoveToNextRow())
    {
        GetAccountFromSql(account, query);
        ret = 0;
    }
    ReleaseDBConn(conn);

    return ret;    
}

int ResidentPersonalAccount::InitAccountById(int id, ResidentPerAccount &account)
{
    std::stringstream sql;
    sql << "select "<< account_sec <<" from PersonalAccount P where ID = " << id;

    int ret = -1;
    GET_DB_CONN_ERR_RETURN(tmp_conn, -1)
    CRldbQuery query(tmp_conn.get());
    query.Query(sql.str());
    if (query.MoveToNextRow())
    {
        GetAccountFromSql(account, query);
        ret = 0;
    }
    return ret;    
}

int ResidentPersonalAccount::InitAccountByUuid(const std::string& uuid, ResidentPerAccount &account)
{
    std::stringstream sql;
    sql << "select "<< account_sec <<" from PersonalAccount P where UUID = '" << uuid << "'";

    int ret = -1;
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    if (query.MoveToNextRow())
    {
        GetAccountFromSql(account, query);
        ret = 0;
    }
    else
    {
        AK_LOG_WARN << "Get Uuid info error, Uuid not exist. Uuid=" << uuid;
    }
    ReleaseDBConn(conn);

    return ret;    
}

int ResidentPersonalAccount::InitAccountByUser(const std::string& user, ResidentPerAccount &account)
{
    auto pos = user.find('@');
    if (pos == std::string::npos)
    {
        return InitAccountByUid(user, account);
    }
    else
    {
        PerAccountUserInfo per_account_user;
        if (0 == dbinterface::PersonalAccountUserInfo::GetPerAccountInfoByEmail(user, per_account_user))
        {
            return InitAccountByUid(per_account_user.main_user_account, account);
        }
    }

    return -1;
}

//当负载高时数据库主从未同步,若创建用户查询命中从库,会导致邮件用户名为空
int ResidentPersonalAccount::InitAccountFromMasterByUser(const std::string& user, ResidentPerAccount &account)
{
    auto pos = user.find('@');
    if (pos == std::string::npos)
    {
        return InitAccountByUidFromMasterDB(user, account);
    }
    else
    {
        PerAccountUserInfo per_account_user;
        if (0 == dbinterface::PersonalAccountUserInfo::GetPerAccountInfoByEmailFromMasterDB(user, per_account_user))
        {
            return InitAccountByUidFromMasterDB(per_account_user.main_user_account, account);
        }
    }

    return -1;
}

int ResidentPersonalAccount::InitAccountByEmail(const std::string& email, ResidentPerAccount &account)
{
    PerAccountUserInfo per_account_user;
    //查找主站点的account
    if (0 == dbinterface::PersonalAccountUserInfo::GetPerAccountInfoByEmail(email, per_account_user))
    {
        return InitAccountByUid(per_account_user.main_user_account, account);
    }

    return -1;
}

int ResidentPersonalAccount::InitAccountByPhone(const std::string& phone_number, ResidentPerAccount &account, PhoneUserType user_type)
{
    if (user_type == PhoneUserType::END_USER)
    {
        PerAccountUserInfo per_account_user;
        //查找主站点的account
        if (0 == dbinterface::PersonalAccountUserInfo::GetPerAccountInfoByMobileNumber(phone_number, per_account_user))
        {
            return InitAccountByUid(per_account_user.main_user_account, account);
        }
    }
    else if (user_type == PhoneUserType::ADMIN)
    {
        //查找管理员账号
        UserInfoAccount user_info_account;
        if (0 == dbinterface::AccountUserInfo::GetAccountUserInfoOnlyByPhone(phone_number, user_info_account))
        {
            return InitAccountByUid(user_info_account.main_user_account, account);
        }
    }
    return -1;
}

void ResidentPersonalAccount::InitUserInfo(ResidentPerAccount& account)
{
    PerAccountUserInfo per_account_user;
    if (0 == dbinterface::PersonalAccountUserInfo::GetPerAccountInfoByUUID(account.user_info_uuid, per_account_user))
    {
        //Snprintf(account.mobile_number, sizeof(account.mobile_number), per_account_user.mobile_number);
        //Snprintf(account.email, sizeof(account.email), per_account_user.email);
    }
}

int ResidentPersonalAccount::InitAccountByRoomID(int room_id, ResidentPerAccount &account)
{
    std::stringstream sql;
    sql << "select "<< account_sec <<" from PersonalAccount P where RoomID = " << room_id;

    int ret = -1;
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    if (query.MoveToNextRow())
    {
        GetAccountFromSql(account, query);
        ret = 0;
    }
    else
    {
        AK_LOG_WARN << "Get RoomID info error, RoomID not exist. RoomID=" << room_id;
    }
    ReleaseDBConn(conn);

    return ret;    
}


int ResidentPersonalAccount::GetUidAccount(const std::string &uid, ResidentPerAccount& account)
{
    int ret = InitAccountByUid(uid, account);
    return ret;
}

int ResidentPersonalAccount::GetExpireTime(std::string &expire_time, ResidentPerAccount& account)
{
    if (account.exp_stamp == 0) //证明已经大于2038年,导致unix_timestamp溢出了.直接用数据库中的过期时间,不管时区转换的问题
    {
        expire_time = account.expire_time;
    }
    else
    {
        std::string time_dst = GetDateByTimeZoneStr(account.timezone, account.exp_stamp);
        expire_time = time_dst;
    }
    return 0;
}

int ResidentPersonalAccount::GetPhoneExpireTime(std::string &phone_expire_time, ResidentPerAccount& account)
{
    if (account.phone_exp_stamp == 0) //证明已经大于2038年,导致unix_timestamp溢出了.直接用数据库中的过期时间,不管时区转换的问题
    {
        phone_expire_time = account.phone_expire_time;
    }
    else
    {
        std::string time_dst = GetDateByTimeZoneStr(account.timezone, account.phone_exp_stamp);
        phone_expire_time = time_dst;
    }
    return 0;
}

//根据node获取所有从账号列表+主账号
int ResidentPersonalAccount::GetAttendantListByUid(const std::string &node, std::set<std::string>& uids)
{
    std::stringstream sql;
    sql << "select B.SipAccount from PersonalAccount A "
        << "left join PersonalAccount B on A.UUID = B.ParentUUID "
        << "where A.Account = '" << node
        << "' and B.Role in(" << ACCOUNT_ROLE_PERSONNAL_ATTENDANT << ","<< ACCOUNT_ROLE_COMMUNITY_ATTENDANT << ")";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    while (query.MoveToNextRow())
    {
        char uid[24] = {0};
        Snprintf(uid, sizeof(uid),  query.GetRowData(0));
        uids.insert(uid);
    }
    uids.insert(node);
    ReleaseDBConn(conn);
    return 0;
}

//根据node获取所有从账号列表+主账号uuid与账号
int ResidentPersonalAccount::GetAttendantListUUIDByUid(const std::string &node, std::vector<ResidentPerAccount>& accounts)
{
    // 获取数据库连接
    GET_DB_CONN_ERR_RETURN(conn, -1);
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    CRldbQuery query(tmp_conn);

    // 获取主账号信息
    std::stringstream sql_main;
    sql_main << "SELECT " << account_sec << " FROM PersonalAccount P WHERE Account = '" << node << "'";
    query.Query(sql_main.str());

    std::string main_uuid; // 主账号的 UUID
    if (query.MoveToNextRow())
    {
        ResidentPerAccount account;
        GetAccountFromSql(account, query);
        accounts.push_back(account);

        main_uuid = account.uuid; // 保存主账号的 UUID
    }

    if (main_uuid.empty())
    {
        AK_LOG_WARN << "Main account not found for node: " << node;
        return -1; // 如果主账号不存在，直接返回
    }

    // 根据主账号 UUID 查询从账号
    std::stringstream sql_sub;
    sql_sub << "SELECT " << account_sec << " FROM PersonalAccount P WHERE ParentUUID = '" << main_uuid
            << "' AND P.Role IN (" << ACCOUNT_ROLE_PERSONNAL_ATTENDANT << "," << ACCOUNT_ROLE_COMMUNITY_ATTENDANT << ")";
    query.Query(sql_sub.str());

    while (query.MoveToNextRow())
    {
        ResidentPerAccount account;
        GetAccountFromSql(account, query);
        accounts.push_back(account);
    }

    return 0;
}

int ResidentPersonalAccount::GetUserAccount(const std::string &struser, ResidentPerAccount& account)
{
    return InitAccountByUser(struser, account);
}

int ResidentPersonalAccount::GetUserAccountFromMaster(const std::string &struser, ResidentPerAccount& account)
{
    return InitAccountFromMasterByUser(struser, account);
}

int ResidentPersonalAccount::GetAccountByID(int id, ResidentPerAccount& account)
{
    return InitAccountById(id, account);
}

int ResidentPersonalAccount::GetUUIDAccount(const std::string &uuid, ResidentPerAccount& account)
{
    return  InitAccountByUuid(uuid, account);
}

int ResidentPersonalAccount::GetPhoneAccount(const std::string &phone, ResidentPerAccount& account, PhoneUserType user_type)
{
    return InitAccountByPhone(phone, account, user_type);
}

int ResidentPersonalAccount::GetEmailAccount(const std::string &email, ResidentPerAccount& account)
{
    int ret = InitAccountByEmail(email, account);
    InitUserInfo(account);
    return ret;
}

int ResidentPersonalAccount::GetRoomIDAccount(int room_id, ResidentPerAccount& account)
{
    return InitAccountByRoomID(room_id, account);
}

int ResidentPersonalAccount::GetAccountListByUserInfoUUID(const std::string &userinfo_uuid, ResidentPerAccountList& account_list)
{
    std::stringstream sql;
    sql << "select " << account_sec <<" from PersonalAccount P where UserInfoUUID = '" << userinfo_uuid << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    while (query.MoveToNextRow())
    {
        ResidentPerAccount tmp_account;
        GetAccountFromSql(tmp_account, query);
        account_list.push_back(tmp_account);
    }
    ReleaseDBConn(conn);

    return 0;
}


bool ResidentPersonalAccount::TestFlag(ResidentPerAccount& account, int flag)
{
    if (strlen(account.account) == 0)
    {
        AK_LOG_WARN << "TestFlag account is null.";
        return -1;
    }
    ResidentPersonalAccountCnf *cnf = &account.cnf;
    assert(cnf->init_status);
    return SwitchHandle(cnf->flags, flag);
}

int ResidentPersonalAccount::GetUUIDByAccount(const std::string& account, std::string& uuid)
{
    std::stringstream sql;
    sql << "select UUID from PersonalAccount where Account = '" << account << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    if (query.MoveToNextRow())
    {
       uuid = query.GetRowData(0);
    }
    ReleaseDBConn(conn);

    return 0;    
}

std::string ResidentPersonalAccount::GetUserInfoUUIDByAccount(const std::string& account)
{
    std::stringstream sql;
    sql << "select UserInfoUUID from PersonalAccount where Account = '" << account << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return "";
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    std::string uuid;
    if (query.MoveToNextRow())
    {
       uuid = query.GetRowData(0);
    }
    ReleaseDBConn(conn);

    return uuid;    
}

int ResidentPersonalAccount::GetAccountByUUID(const std::string& uuid, std::string& account)
{
    std::stringstream sql;
    sql << "select Account from PersonalAccount where UUID = '"  << uuid << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    if (query.MoveToNextRow())
    {
       account = query.GetRowData(0);
    }
    ReleaseDBConn(conn);

    return 0;    
}

int ResidentPersonalAccount::CheckAccountIsExpire(const std::string& account)
{
    if(account.size() == 0)
    {
        return 0;
    }
    
    std::stringstream streamsql;
    streamsql << "select Active, ExpireTime < now() as Expire from PersonalAccount where Account = '"<< account << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return 0;
    }

    CRldbQuery query(tmp_conn);
    query.Query(streamsql.str());
    
    int is_active = 0;
    int is_expire = 0;
    if (query.MoveToNextRow())
    {
        is_active = ATOI(query.GetRowData(0));
        is_expire = ATOI(query.GetRowData(1));
    }
    else
    {
        ReleaseDBConn(conn);
        return 0;
    }
    
    ReleaseDBConn(conn);
    if((is_active && is_expire) || !is_active)
    {
        return 1;
    }
    else
    {
        return 0;
    }
}

int ResidentPersonalAccount::GetExpireTimeByUUID(const std::string &uuid, std::string& expire_time, int type)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(tmp_conn);

    std::stringstream streamsql;
    if (type == ACCOUNT_APP_EXPIRE)
    {
        streamsql << "select unix_timestamp(ExpireTime), TimeZone,ExpireTime from PersonalAccount where UUID = '" << uuid << "'";
    }
    else
    {
        streamsql << "select unix_timestamp(PhoneExpireTime), TimeZone,PhoneExpireTime from PersonalAccount where UUID = '" << uuid << "'";
    }
    
    query.Query(streamsql.str());
    if (query.MoveToNextRow())
    {
        std::time_t timestamp_beijing = 0;
        char time_zone_str[36] = {0};

        timestamp_beijing = ATOI(query.GetRowData(0));
        if (timestamp_beijing == 0) //证明已经大于2038年,导致unix_timestamp溢出了.直接用数据库中的过期时间,不管时区转换的问题
        {
            expire_time = query.GetRowData(2);
        }
        else
        {
            Snprintf(time_zone_str, sizeof(time_zone_str),  query.GetRowData(1));
            expire_time = GetDateByTimeZoneStr(time_zone_str, timestamp_beijing);
        }
    }

    ReleaseDBConn(conn);
    return 0;
}

int ResidentPersonalAccount::UpdateAppLoginStatus(const std::string& user)
{
    std::stringstream streamsql;
    streamsql << "update PersonalAccount set appLoginStatus=1,Initialization=1 where Account = '" << user << "'";
              
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    if (conn->Execute(streamsql.str()) < 0)
    {
        ReleaseDBConn(conn);
        AK_LOG_WARN << "Failed to insert new record into db,SQL is " << streamsql.str();
        return -1;
    }
    //为减少对接口调用效率的影响,此记录表不考虑node字段的记录了
    std::stringstream streamsql1;
    streamsql1 << "INSERT INTO AppLoginLog" << " (Uid) VALUE ('" << user  << "');";

    if (conn->Execute(streamsql1.str()) < 0)
    {
        ReleaseDBConn(conn);
        return -1;
    }
    
    ReleaseDBConn(conn);
    return 0;
}

int ResidentPersonalAccount::GetAppPayMode(const std::string& uid)
{
/*  下面代码对从账户会有问题
    std::stringstream streamsql;
    int charge_mode = 0;
    streamsql << "select AA.ChargeMode as dis, A.ChargeMode as ins from PersonalAccount P "
              << "left join Account A on A.ID=P.ParentID "
              << "left join Account AA on A.ParentID = AA.ID where P.Account='"
              << uid
              << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(tmp_conn);

    query.Query(streamsql.str());
    if (query.MoveToNextRow())
    {
        int dis = ATOI(query.GetRowData(0));
        int ins = ATOI(query.GetRowData(1));
        if (dis == 1)
        {
            charge_mode = 1;//本级付费，对于终端就是不显示
        }
        else
        {
            charge_mode = ins;
        }
    }
    ReleaseDBConn(conn);
    return charge_mode;
    */
   return 1;
}

bool ResidentPersonalAccount::CheckJpRedirect(const std::string &account)
{
    int dis_id = 0;
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        return false;
    }
    CRldbQuery query(tmp_conn);

    std::stringstream streamSQL;
    streamSQL << "select Role, ParentID from PersonalAccount where Account =  " << account;
    query.Query(streamSQL.str());

    if (!query.MoveToNextRow())
    {
        ReleaseDBConn(conn);
        return false;
    }

    int role = ATOI(query.GetRowData(0));
    int parent_id = ATOI(query.GetRowData(1));
    
    int community_id = 0;
    if(role == ACCOUNT_ROLE_COMMUNITY_MAIN || role == ACCOUNT_ROLE_PERSONNAL_MAIN \
        || role == ACCOUNT_ROLE_COMMUNITY_PM || IsOfficeRole(role))
    {
        community_id = parent_id; 
    }
    else
    {
        std::stringstream sql;
        sql << "select A.ID FROM Account A join PersonalAccount P ON P.ParentID = A.ID WHERE P.ID =" << parent_id;
        
        query.Query(sql.str());
        if (query.MoveToNextRow())
        {
            community_id = ATOI(query.GetRowData(0));
        }
    }

    std::stringstream sql;
    sql << "select ParentID FROM Account WHERE ID =" << community_id;
    
    query.Query(sql.str());
    if (query.MoveToNextRow())
    {
        dis_id = ATOI(query.GetRowData(0));
    }
    else
    {
        ReleaseDBConn(conn);
        return false;
    }

    std::stringstream sql2;
    sql2 << "select ID from AwsRedirect where AccountID = " << dis_id;
    query.Query(sql2.str());

    if (!query.MoveToNextRow())
    {       
        ReleaseDBConn(conn);
        return false;
    }
    
    AK_LOG_INFO << "DaoCheckJpRedirect return Yes, uid:" << account;
    ReleaseDBConn(conn);
    return true;
}

int ResidentPersonalAccount::GetPhoneInfoList(const std::string& phone, std::vector<PersonalPhoneInfo> &phone_info_list)
{
    // 如果短于这个值，应该是错误的号码，不能往下匹配，会匹配很多号码，而导致出错。
    if (phone.length() < PHONE_DETECT_NUM)
    {
        return -1;
    }

    // 取7位尾号进行匹配，因为传来的phone前面可能带有区号等
    std::string detect_phone = GetSubstrFromBehind(phone, PHONE_DETECT_NUM);

    // 从mapping库中模糊查询出尾号匹配的phone
    std::vector<std::string> encrypt_phone_list;
    dbinterface::PhoneMapping::FuzzySearchPhone(detect_phone, encrypt_phone_list);

    // 通过尾号匹配的phone查出账号信息
    ResidentPerAccountList account_list;
    GetAccountListByPhoneList(encrypt_phone_list, account_list);

    std::vector<PersonalPhoneInfo> phone_infos;
    for (const auto& account : account_list)
    {
        PersonalPhoneInfo phone_info;
        
        phone_info.role = account.role;
        phone_info.unit_id = account.unit_id;
        phone_info.mng_id = account.parent_id;
        Snprintf(phone_info.name, sizeof(phone_info.name),  account.name);
        Snprintf(phone_info.node, sizeof(phone_info.node), account.account);
        Snprintf(phone_info.account, sizeof(phone_info.account), account.account);
        Snprintf(phone_info.account_uuid, sizeof(phone_info.account_uuid), account.uuid);
        Snprintf(phone_info.parent_uuid, sizeof(phone_info.parent_uuid),  account.parent_uuid);
        Snprintf(phone_info.user_info_uuid, sizeof(phone_info.user_info_uuid), account.user_info_uuid);

        std::string phone = std::string(account.phone_code) + std::string(account.phone);
        if (phone.length() >  PHONE_DETECT_NUM)
        {
            Snprintf(phone_info.phone, sizeof(phone_info.phone), phone.c_str());
            phone_infos.push_back(phone_info);
        }

        std::string phone2 = std::string(account.phone_code) + std::string(account.phone2);
        if (phone.length() >  PHONE_DETECT_NUM)
        {
            Snprintf(phone_info.phone, sizeof(phone_info.phone), phone2.c_str());
            phone_infos.push_back(phone_info);
        }

        std::string phone3 = std::string(account.phone_code) + std::string(account.phone3);
        if (phone.length() >  PHONE_DETECT_NUM)
        {
            Snprintf(phone_info.phone, sizeof(phone_info.phone), phone3.c_str());
            phone_infos.push_back(phone_info);
        }
    }
    
    // phonecode+phone取最大匹配
    int max_match_len = -1;   
    for (auto& phone_info : phone_infos)
    {
        int macth_len = GetStrMatchNumFromBehind(phone, phone_info.phone);
        
        phone_info.match_num = macth_len;
        
        max_match_len = macth_len > max_match_len ? macth_len : max_match_len;
    }
    
    // 获取号码相同的账号(匹配度一样为最高)
    for (auto& phone_info : phone_infos)
    {
        if (phone_info.match_num == max_match_len)   
        {
            phone_info_list.push_back(phone_info);
        }
    }

    for (auto& phone_info : phone_info_list)
    {
        if (phone_info.role == ACCOUNT_ROLE_PERSONNAL_ATTENDANT || phone_info.role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT)
        {
            ResidentPerAccount node_account;
            if (0 == GetUUIDAccount(phone_info.parent_uuid, node_account)) 
            {
                phone_info.mng_id = node_account.parent_id;
                Snprintf(phone_info.node, sizeof(phone_info.node), node_account.account);
            }
        }
    }

    return 0;
}

//只返回一个匹配值
int ResidentPersonalAccount::GetPhoneInfoByMngID(const std::string& phone, unsigned int mng_id, PersonalPhoneInfo &phone_info)
{   
    int match_num = 0;
    std::vector<PersonalPhoneInfo> phonelist;
    GetPhoneInfoList(phone, phonelist);
    for (auto& phone : phonelist)
    {
        if (phone.mng_id == mng_id)
        {
            match_num++;
            memcpy(&phone_info, &phone, sizeof(phone));
        }
    }
    
    if (match_num <= 0)
    {
        return -1;
    }

    if (match_num > 1)
    {
        AK_LOG_WARN << "Get phone info have match several number=" << match_num << " phone=" << phone;
    }
    
    return 0;
}

//只返回一个匹配值
int ResidentPersonalAccount::GetPhoneInfoByNode(const std::string& phone, const std::string &node, PersonalPhoneInfo &phone_info)
{   
    int match_num = 0;
    std::vector<PersonalPhoneInfo> phonelist;
    GetPhoneInfoList(phone, phonelist);
    for (auto& phone : phonelist)
    {
        if (strcmp(phone.node, node.c_str()) == 0)
        {
            match_num++;
            memcpy(&phone_info, &phone, sizeof(phone));
        }
    }
    
    if (match_num > 1)
    {
        AK_LOG_WARN << "get phone info have match several number=" << match_num << " phone=" << phone;
    }

    return 0;
}

//通过node-code查找持卡人的昵称
//个人设备的nfc
//V4.3 加入BLE
std::string ResidentPersonalAccount::GetNFcNameForPerDevNFC(const std::string& node, const std::string& code)
{
    std::string name;
    char sql[1024] = {0};
    ::snprintf(sql, 1024, "select A.Name, A.UUID From PersonalAccount A left join PersonalAccount B on B.ID=A.ParentID and (B.Role=%d or B.Role=%d) \
    where B.Account='%s' and (A.NFCCode='%s' or A.BLECode='%s') \
    union select Name, UUID from PersonalAccount where Account='%s' and (NFCCode='%s' or BLECode='%s');",
    ACCOUNT_ROLE_PERSONNAL_MAIN, ACCOUNT_ROLE_COMMUNITY_MAIN,
    node.c_str(), code.c_str(), code.c_str(),
    node.c_str(), code.c_str(), code.c_str());

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return name;
    }
    CRldbQuery query(tmp_conn);
    std::string sql2 = sql;
    query.Query(sql2);

    if (query.MoveToNextRow())
    {
        name = dbinterface::DataConfusion::Decrypt(query.GetRowData(0));
    }
    ReleaseDBConn(conn);
    return name;

}

int ResidentPersonalAccount::GetFaceInfoByNodeAndName(const std::string &node, const std::string &name,
                                                      PersonalFaceKeyInfo& key_info)
{
    std::string encrypt_name = dbinterface::DataConfusion::Encrypt(name.c_str());
    char sql[1024] = {0};
    ::snprintf(sql, 1024, "select A.Name, A.UUID From PersonalAccount A left join PersonalAccount B on B.UUID=A.ParentUUID and (B.Role=%d or B.Role=%d) \
    where B.Account='%s' and A.name='%s' \
    union select Name, UUID from PersonalAccount where Account='%s' and Name='%s';",
    ACCOUNT_ROLE_PERSONNAL_MAIN, ACCOUNT_ROLE_COMMUNITY_MAIN,
    node.c_str(), encrypt_name.c_str(), node.c_str(), encrypt_name.c_str());
    
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(tmp_conn);
    std::string sql2 = sql;
    query.Query(sql2);

    if (query.MoveToNextRow())
    {
        Snprintf(key_info.account_name, sizeof(key_info.account_name), dbinterface::DataConfusion::Decrypt(query.GetRowData(0)).c_str());
        Snprintf(key_info.account_uuid, sizeof(key_info.account_uuid), query.GetRowData(1));
    }
    ReleaseDBConn(conn);

    return 0;
}


int ResidentPersonalAccount::GetNfcInfoByNodeAndCode(const std::string& node, const std::string& code,
                                                    PersonalNfcKeyInfo &key_info)
{
    char sql[1024] = {0};
    ::snprintf(sql, 1024, "select A.Name, A.UUID From PersonalAccount A left join PersonalAccount B on B.ID=A.ParentID and (B.Role=%d or B.Role=%d) \
    where B.Account='%s' and (A.NFCCode='%s' or A.BLECode='%s')\
    union select Name, UUID from PersonalAccount where Account='%s' and (NFCCode='%s' or BLECode='%s');",
    ACCOUNT_ROLE_PERSONNAL_MAIN, ACCOUNT_ROLE_COMMUNITY_MAIN,
    node.c_str(), code.c_str(), code.c_str(),
    node.c_str(), code.c_str(), code.c_str());
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(tmp_conn);
    std::string sql2 = sql;
    query.Query(sql2);

    if (query.MoveToNextRow())
    {
        Snprintf(key_info.account_name, sizeof(key_info.account_name), dbinterface::DataConfusion::Decrypt(query.GetRowData(0)).c_str());
        Snprintf(key_info.account_uuid, sizeof(key_info.account_uuid), query.GetRowData(1));
    }
    ReleaseDBConn(conn);
    return 0;

}

//梯口机 nfc
//V4.3 加入BLE
std::string ResidentPersonalAccount::GetNFCNameAndNodeForUnitPubDev(int unitID, const std::string& code, std::string& node)
{
    std::string name;
    char sql[1024] = {0};
    ::snprintf(sql, 1024, "select Name,Role,ParentID,Account From PersonalAccount where NFCCode='%s' or BLECode='%s';", code.c_str(), code.c_str());

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return name;
    }
    CRldbQuery query(tmp_conn);
    std::string sql2 = sql;
    query.Query(sql2);

    int role = 0;
    int parent_id = 0;
    if (query.MoveToNextRow())
    {
        name = dbinterface::DataConfusion::Decrypt(query.GetRowData(0));
        role = ATOI(query.GetRowData(1));
        parent_id = ATOI(query.GetRowData(2));

        // pm 直接返回名称
        if (role == ACCOUNT_ROLE_COMMUNITY_PM)
        {
            node = query.GetRowData(3);
            ReleaseDBConn(conn);
            return name;
        }

        // 主从账号
        if (!(role == ACCOUNT_ROLE_PERSONNAL_ATTENDANT || role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT)) //主账号
        {
            //非社区从、单住户从
            node = query.GetRowData(3);
        }
        else
        {
            //如果是从账号,则查询出对应的主账号
            std::stringstream streamSQL2;
            streamSQL2 << "SELECT Account from PersonalAccount WHERE ID = '"<< parent_id << "'";

            query.Query(streamSQL2.str());
            //终端用户名是全局唯一的,故用if not while
            if (query.MoveToNextRow())
            {
                node = query.GetRowData(0);
            }
            else
            {
                AK_LOG_WARN << "There is not main account belong to in db.";
                ReleaseDBConn(conn);
                return name;
            }
        }
    }
    ReleaseDBConn(conn);
    return name;
}

//开公共设备nfc
//V4.3 加入BLE
std::string ResidentPersonalAccount::GetNFCNameAndNodeForPubDev(int mngID, const std::string& code, std::string& node)
{
    std::string name;
    char sql[1024] = {0};
    ::snprintf(sql, 1024, "select A.Name,A.Role,A.ParentID,A.Account From PersonalAccount A left join PersonalAccount B on B.ID=A.ParentID \
    and (B.Role=%d or B.Role=%d or B.Role=%d) where B.ParentID=%d and (A.NFCCode='%s' or A.BLECode='%s') \
    union select Name,Role,ParentID,Account from PersonalAccount where ParentID=%d and (Role=%d or Role=%d or Role=%d) and (NFCCode='%s' or BLECode='%s');",
    ACCOUNT_ROLE_PERSONNAL_MAIN, ACCOUNT_ROLE_COMMUNITY_MAIN, ACCOUNT_ROLE_COMMUNITY_PM,
    mngID, code.c_str(), code.c_str(),
    mngID, ACCOUNT_ROLE_PERSONNAL_MAIN, ACCOUNT_ROLE_COMMUNITY_MAIN, ACCOUNT_ROLE_COMMUNITY_PM, code.c_str(), code.c_str());

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return name;
    }
    CRldbQuery query(tmp_conn);
    std::string sql2 = sql;
    query.Query(sql2);
    int role = 0;
    int parent_id = 0;

    if (query.MoveToNextRow())
    {
        name = dbinterface::DataConfusion::Decrypt(query.GetRowData(0));
        role = ATOI(query.GetRowData(1));
        parent_id = ATOI(query.GetRowData(2));
        
        // pm 直接返回名称
        if (role == ACCOUNT_ROLE_COMMUNITY_PM)
        {
            node = query.GetRowData(3);
            ReleaseDBConn(conn);
            return name;
        }

        if (!(role == ACCOUNT_ROLE_PERSONNAL_ATTENDANT || role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT)) //主账号
        {
            node = query.GetRowData(3);
        }
        else
        {
            //如果是从账号,则查询出对应的主账号
            std::stringstream streamSQL2;
            streamSQL2 << "SELECT Account from PersonalAccount WHERE ID = '" << parent_id << "'";

            query.Query(streamSQL2.str());
            //终端用户名是全局唯一的,故用if not while
            if (query.MoveToNextRow())
            {
                node = query.GetRowData(0);
            }
            else
            {
                AK_LOG_WARN << "There is not main account belong to in db.";
                ReleaseDBConn(conn);
                return name;
            }
        }
    }
    ReleaseDBConn(conn);
    return name;

}

std::string ResidentPersonalAccount::GetNodeTimeZoneStr(const std::string& node)
{
    std::stringstream streamsql;
    streamsql << "select P.TimeZone, P.CustomizeForm, A.TimeZone, P.Role from PersonalAccount P join Account A on P.ParentID = A.ID where P.Account = '" << node << "'";

    std::string timezone;
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return timezone;//0时区
    }
    CRldbQuery query(tmp_conn);
    query.Query(streamsql.str());

    if (query.MoveToNextRow())
    {
        int role = ATOI(query.GetRowData(3));     
        if(ACCOUNT_ROLE_PERSONNAL_MAIN ==  role)
        {
            timezone = query.GetRowData(0);
        }
        else    //社区帐号用社区时区
        {
            timezone = query.GetRowData(2);
        }
    }

    ReleaseDBConn(conn);
    return timezone;
}

std::string ResidentPersonalAccount::GetTimeZoneByUUID(const std::string& uuid)
{
    std::string timezone;
    std::stringstream stream_sql;
    stream_sql << "select P.TimeZone, P.CustomizeForm, A.TimeZone, P.Role from PersonalAccount P join Account A on P.ParentUUID = A.UUID where P.UUID = '" << uuid << "'";

    GET_DB_CONN_ERR_RETURN(db_conn, timezone);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        int role = ATOI(query.GetRowData(3));     
        if (ACCOUNT_ROLE_PERSONNAL_MAIN ==  role)
        {
            timezone = query.GetRowData(0);
        }
        else    //社区帐号用社区时区
        {
            timezone = query.GetRowData(2);
        }
    }
    else
    {
        AK_LOG_WARN << "GetTimeZoneByUUID by PersonalAccountUUID failed, PersonalAccountUUID = " << uuid;
        return "";
    }

    return timezone;
}

int ResidentPersonalAccount::GetAccountListByPhoneList(const std::vector<std::string>& phone_list, ResidentPerAccountList& account_list)
{       
    GET_DB_CONN_ERR_RETURN(db_conn, -1)
    CRldbQuery query(db_conn.get());

    for (const auto& phone : phone_list)
    {
        std::stringstream stream_sql;
        stream_sql << "select " << account_sec << " from PersonalAccount P where Phone = '" << phone << "' or Phone2 = '" << phone << "' or Phone3 = '" << phone << "'";

        query.Query(stream_sql.str());
        while (query.MoveToNextRow())
        {
            ResidentPerAccount account{};
            GetAccountFromSql(account, query);
            account_list.push_back(account);
        }
    }
    return 0;
}

//office 的不在这里处理
int ResidentPersonalAccount::GetNodeActiveByPhone(const std::string& phone, const std::string& caller)
{
    if (phone.empty() || caller.empty())
    {
        AK_LOG_WARN << "parameter error! phone or caller is null!";
        return 0;
    }

    // 如果短于这个值，应该是错误的号码，不能往下匹配，会匹配很多号码，而导致出错。
    if (phone.length() < PHONE_DETECT_NUM)
    {
        AK_LOG_WARN << "phone length < detect length";
        return 0;
    }
    
    // 取7位尾号进行匹配，因为传来的phone前面可能带有区号等，区号长度不固定。
    // 可能出现匹配到其他用户的场景，但后面会根据主叫(设备)筛选掉跨社区的情况，
    // 因此只有当同小区下存在一个落地过期、一个未过期的用户才会出问题。概率较低。
    std::string detect_phone = GetSubstrFromBehind(phone, PHONE_DETECT_NUM);

    // 从mapping库中模糊查询出尾号匹配的phone
    std::vector<std::string> encrypt_phone_list;
    dbinterface::PhoneMapping::FuzzySearchPhone(detect_phone, encrypt_phone_list);

    // 通过尾号匹配的phone查出账号信息
    ResidentPerAccountList account_list;
    GetAccountListByPhoneList(encrypt_phone_list, account_list);
    
    // 初始化phone account消息
    std::vector<PersonalPhoneInfo> phone_account_list;
    for (const auto& account : account_list)
    {
        PersonalPhoneInfo phone_account{};

        phone_account.role = account.role;
        phone_account.node_active = account.active;
        phone_account.mng_id = account.parent_id;
        phone_account.node_switch = account.switch_value;
        phone_account.node_account_expire = account.is_expire;
        phone_account.node_landline_expire = account.is_feature_expire;
        Snprintf(phone_account.account, sizeof(phone_account.account), account.account);
        Snprintf(phone_account.parent_uuid, sizeof(phone_account.parent_uuid), account.parent_uuid);
    
        std::string phone = std::string(account.phone_code) + std::string(account.phone);
        if (phone.length() > PHONE_DETECT_NUM)
        {
            Snprintf(phone_account.phone, sizeof(phone_account.phone), phone.c_str());
            phone_account_list.push_back(phone_account);
        }

        std::string phone2 = std::string(account.phone_code) + std::string(account.phone2);
        if (phone2.length() > PHONE_DETECT_NUM)
        {
            Snprintf(phone_account.phone, sizeof(phone_account.phone), phone2.c_str());
            phone_account_list.push_back(phone_account);
        }
        
        std::string phone3 = std::string(account.phone_code) + std::string(account.phone3);
        if (phone3.length() > PHONE_DETECT_NUM)
        {
            Snprintf(phone_account.phone, sizeof(phone_account.phone), phone3.c_str());
            phone_account_list.push_back(phone_account);
        }
    }

    // 获取phone匹配长度的最大值
    int max_match_length = 0;
    for (auto& phone_account : phone_account_list)
    {
        int match_length = GetStrMatchNumFromBehind(phone, phone_account.phone);
        phone_account.match_num = match_length;
        max_match_length = match_length > max_match_length ? match_length : max_match_length;
    }

    // 获取phone匹配度最高的账号列表
    std::vector<PersonalPhoneInfo> match_phone_account_list;
    for (auto& phone_account : phone_account_list)
    {
        PersonalPhoneInfo detect_account = static_cast<PersonalPhoneInfo>(phone_account);

        // 获取号码相同的账号(匹配度一样为最高)
        if (detect_account.match_num == max_match_length)   
        {
            match_phone_account_list.push_back(detect_account);
        }
    }

    // 查询node消息
    for (auto& match_phone_account : match_phone_account_list)
    {
        if (match_phone_account.role == ACCOUNT_ROLE_COMMUNITY_PM || match_phone_account.role == ACCOUNT_ROLE_COMMUNITY_MAIN || match_phone_account.role == ACCOUNT_ROLE_PERSONNAL_MAIN)
        {
            Snprintf(match_phone_account.node, sizeof(match_phone_account.node), match_phone_account.account);
        }
        else if (match_phone_account.role == ACCOUNT_ROLE_PERSONNAL_ATTENDANT || match_phone_account.role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT)
        {
            ResidentPerAccount node_account;
            if (0 == GetUUIDAccount(match_phone_account.parent_uuid, node_account)) 
            {
                match_phone_account.mng_id = node_account.parent_id;
                match_phone_account.node_switch = node_account.switch_value;
                match_phone_account.node_active = node_account.active;
                match_phone_account.node_account_expire = node_account.is_expire;
                match_phone_account.node_landline_expire = node_account.is_feature_expire;
                Snprintf(match_phone_account.node, sizeof(match_phone_account.node), node_account.account);
            }
        }
    }

    // 存在相同号码才进行根据caller筛查
    if (match_phone_account_list.size()) 
    {
        int is_per = 0;
        int dev_flag = 0;
        unsigned int mng_id = 0;
        char per_node[32] = {0};
        
        //在实际使用中呼叫落地的主叫都是设备
        ResidentDev dev;
        if (0 == dbinterface::ResidentPerDevices::GetDevicesBySip(caller, dev))
        {
            is_per = 1;
            dev_flag = dev.flag;
            Snprintf(per_node, sizeof(per_node), dev.node);
        }
        else if (0 == dbinterface::ResidentDevices::GetDevicesBySip(caller, dev))
        {
            mng_id = dev.project_mng_id;
        }

        // 剔除不匹配的账号
        match_phone_account_list.erase(std::remove_if(match_phone_account_list.begin(), match_phone_account_list.end(), [&](PersonalPhoneInfo& phone_account) -> bool 
            {
                // 主叫是社区的
                if (!is_per) 
                { 
                    return (phone_account.role != ACCOUNT_ROLE_COMMUNITY_PM && phone_account.role != ACCOUNT_ROLE_COMMUNITY_MAIN && phone_account.role != ACCOUNT_ROLE_COMMUNITY_ATTENDANT) || phone_account.mng_id != mng_id;
                } 
                else 
                { 
                    // 主叫是单住户的
                    if(phone_account.role != ACCOUNT_ROLE_PERSONNAL_ATTENDANT && phone_account.role != ACCOUNT_ROLE_PERSONNAL_MAIN) 
                    {
                        return true; 
                    }
                    else 
                    {
                        // 公共设备或个人账号不正确，应该删除
                        return !(dev_flag || strncmp(per_node, phone_account.node, sizeof(per_node)) == 0);
                    }
                }
            }
        ), match_phone_account_list.end());

       // 判断落地功能是否正常
       for (auto const& match_phone_account : match_phone_account_list)
       {
            if (is_per)
            {
                // 单住户主账号高级功能开关
                int feature_plan_switch = SwitchHandle(match_phone_account.node_switch, ResidentPersonalAccount::SwitchType::FEATURE_PLAN);
                if (match_phone_account.node_active && !match_phone_account.node_landline_expire && feature_plan_switch)
                {
                    return 1;
                }
            }
            else
            {
                // dis 没配置按 apt 收费，看社区的配置
                // dis 配置了按 apt 收费，看apt的配置
                int landline_switch = 0;
                DistributorInfoSt distributor_info;
                dbinterface::DistributorInfo::GetDistributorInfoByNode(match_phone_account.node, distributor_info);
                if (distributor_info.enable_apt_charge_plan) {
                    // 判断apt的落地开关
                    PersonalAccountCnfInfo node_cnf;
                    dbinterface::PersonalAccountCnf::GetPeronalAccountCnfByNode(match_phone_account.node, node_cnf);
                    landline_switch = node_cnf.enable_landline;
                } else {
                    // 社区落地开关
                    CommunityInfo community_info(match_phone_account.node);
                    landline_switch = community_info.EnableLandline();
                }
                
                // 我们这里只要判断用户是否过期，因为开了EnableLandline 就需要按月给用户续过期时间
                if (match_phone_account.node_active && !match_phone_account.node_account_expire && landline_switch)
                {
                    return 1;
                }
            }
        }

        AK_LOG_INFO << "GetNodeActiveByPhone failed, maybe not enable feature or not active or already expired, phone = " << phone << ", caller = " << caller;
    }


    AK_LOG_INFO << "GetNodeActiveByPhone failed, not find matched phone, phone = " << phone << ", caller = " << caller;
    return 0;
}

//获取sip对应的落地号码用于国内离线推送兜底
std::string ResidentPersonalAccount::GetPhoneBySip(const std::string& sip, int type, std::string &phone_code)
{
    std::stringstream streamSQL;
    if(type == PERSONAL_SIP)
    {
        streamSQL << "select a.Phone,a.PhoneCode,a.Role,b.CallType,a.Active, a.ExpireTime < now() as Expire from PersonalAccount a left join PersonalAccountCnf b on a.Account = b.Account where a.Account = '"  << sip 
            << "' and a.Role in " << "(" << ACCOUNT_ROLE_COMMUNITY_MAIN << "," << ACCOUNT_ROLE_COMMUNITY_ATTENDANT << "," << ACCOUNT_ROLE_COMMUNITY_PM << ")";
    }
    else
    {
        streamSQL << "select P.Phone,P.PhoneCode from PersonalAccount P join SipGroup2 G on P.Account = G.Account  where G.SipGroup = '" << sip << "' and P.Role = " << ACCOUNT_ROLE_COMMUNITY_MAIN;
    }

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return "";
    }
    CRldbQuery query(tmp_conn);
    query.Query(streamSQL.str());

    std::string phone;
    int call_type = 0;
    int role = 0;
    int active = 0;
    int expire = 0;
    if (query.MoveToNextRow())
    {
        phone =  dbinterface::DataConfusion::Decrypt(query.GetRowData(0)).c_str();
        phone_code = query.GetRowData(1);
        if(type == PERSONAL_SIP)
        {
            role = ATOI(query.GetRowData(2));
            call_type = ATOI(query.GetRowData(3));
            active = ATOI(query.GetRowData(4)); 
            expire = ATOI(query.GetRowData(5));
            //APP未激活或者过期,不能拨打落地电话
            if (0 == active || 1 == expire)
            {
                phone = "";
            }
            //PM的calltype只能在app设置，值为012，当是2时候配置给设备的sip账号，呼叫时候再走兜底
            if (role == ACCOUNT_ROLE_COMMUNITY_PM && call_type != NODE_CALL_TYPE_APP_INDOOR_BACK_PHONE)
            {
                phone = "";
            }
        }
    }
    else
    {
        ReleaseDBConn(conn);
        return "";
    }
    ReleaseDBConn(conn);

    return phone;
}


//根据node获取从账号列表
int ResidentPersonalAccount::GetPersoanlAttendantListByUid(const std::string &node, int role,
    ResidentPerAccountList& account_list)
{
    std::stringstream sql;
    sql << "select B.Name, B.SipAccount, B.UUID, B.Account from PersonalAccount A "
        << "left join PersonalAccount B on A.ID = B.ParentID "
        << "where A.Account = '"
        << node
        << "' AND B.Role = "
        << role;

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    while (query.MoveToNextRow())
    {
        ResidentPerAccount account;
        memset(&account, 0, sizeof(account));
        Snprintf(account.name, sizeof(account.name),  dbinterface::DataConfusion::Decrypt(query.GetRowData(0)).c_str());
        Snprintf(account.sip_account, sizeof(account.sip_account), query.GetRowData(1));
        Snprintf(account.uuid, sizeof(account.uuid), query.GetRowData(2));
        Snprintf(account.account, sizeof(account.account), query.GetRowData(3));
        account_list.push_back(account);
    }

    ReleaseDBConn(conn);
    return 0;
}
//根据node获取家庭App账号列表
int ResidentPersonalAccount::GetNodeUidListByNode(const std::string& node, std::vector<std::string>& uid_list)
{
    if (node.size() == 0)
    {
        AK_LOG_WARN << "get node uid list failed. node is empty";
        return -1;
    }

    uid_list.push_back(node);

    std::stringstream sql;
    sql << "select B.Account from PersonalAccount B "
        << "left join PersonalAccount A on A.UUID = B.ParentUUID "
        << "where A.Account = '" << node << "'"
        << "and (B.Role = " << ACCOUNT_ROLE_COMMUNITY_ATTENDANT << " or B.Role = " << ACCOUNT_ROLE_PERSONNAL_ATTENDANT << ")";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    std::string uid;
    while (query.MoveToNextRow())
    {
        uid = query.GetRowData(0);
        uid_list.push_back(uid);
    }

    ReleaseDBConn(conn);
    return 0;
}

void ResidentPersonalAccount::GetNodeAccountsStrByNode(const std::string& node, std::string& accounts_str)
{
    std::vector<std::string> uid_list;
    if (0 != GetNodeUidListByNode(node, uid_list))
    {
        AK_LOG_WARN << "Get Node Uid List failed.";
        return;
    }

    accounts_str = ListToSeparatedFormatString(uid_list);
    return;
}

// 通过ParentUUID获取 从账号 列表
int ResidentPersonalAccount::GetAttendantUserInfoListByParentUUID(const std::string& parent_uuid, ResidentPerAccountList& attendant_list)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    std::stringstream stream_sql;
    stream_sql << "select " << account_sec << " from PersonalAccount P where ParentUUID = '" << parent_uuid 
               << "' and Role in (" << ACCOUNT_ROLE_PERSONNAL_ATTENDANT << "," <<  ACCOUNT_ROLE_COMMUNITY_ATTENDANT << ")";

    CRldbQuery query(tmp_conn);
    query.Query(stream_sql.str());

    ResidentPerAccount attendant_info;
    while (query.MoveToNextRow())
    {
        memset(&attendant_info, 0, sizeof(attendant_info));
        GetAccountFromSql(attendant_info, query);
        attendant_list.push_back(attendant_info);
    }

    ReleaseDBConn(conn);
    return 0;
}

int ResidentPersonalAccount::GetCommPubMainList(int mng_id, ResidentPerAccountList& account_list)
{
    std::stringstream sql;
    sql << "SELECT Name, SipAccount,RoomNumber,UnitID FROM PersonalAccount "
        << "WHERE ParentID = '" << mng_id << "' AND Role = " << ACCOUNT_ROLE_COMMUNITY_MAIN;

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    while (query.MoveToNextRow())
    {
        ResidentPerAccount account;
        memset(&account, 0, sizeof(account));
        Snprintf(account.name, sizeof(account.name),  dbinterface::DataConfusion::Decrypt(query.GetRowData(0)).c_str());
        Snprintf(account.sip_account, sizeof(account.sip_account), query.GetRowData(1));
        Snprintf(account.room_number, sizeof(account.room_number), query.GetRowData(2));
        account.unit_id = ATOI(query.GetRowData(3));
        account_list.push_back(account);
    }

    ReleaseDBConn(conn);
    return 0;
}

int ResidentPersonalAccount::GetCommUnitMainList(int unit_id, ResidentPerAccountList& account_list)
{
    std::stringstream sql;
    sql << "select Name,SipAccount,RoomNumber from PersonalAccount where UnitID = '" << unit_id << "' AND Role = " << ACCOUNT_ROLE_COMMUNITY_MAIN;

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    while (query.MoveToNextRow())
    {
        ResidentPerAccount account;
        memset(&account, 0, sizeof(account));
        Snprintf(account.name, sizeof(account.name),  dbinterface::DataConfusion::Decrypt(query.GetRowData(0)).c_str());
        Snprintf(account.sip_account, sizeof(account.sip_account), query.GetRowData(1));
        Snprintf(account.room_number, sizeof(account.room_number), query.GetRowData(2));
        account.unit_id = unit_id;
        account_list.push_back(account);
    }

    ReleaseDBConn(conn);
    return 0;
}

int ResidentPersonalAccount::GetCommPmApplistByMngID(int mng_id, ResidentPerAccountList& account_list)
{
    std::stringstream stream_sql;
    stream_sql << "select A.Name,A.SipAccount,A.RoomNumber,A.EnableIpDirect,A.Phone,A.ID,A.Role,A.RoomID,A.PhoneCode,A.Phone2,A.Phone3,A.PhoneStatus,A.Special,B.CallType,A.UUID,A.UserInfoUUID,A.Account from PersonalAccount A "
               << "left join PersonalAccountCnf B on A.Account = B.Account "
               << "left join PmAccountMap C on A.UUID = C.PersonalAccountUUID "
               << "where A.ParentID = " << mng_id << " and A.Role = " << ACCOUNT_ROLE_COMMUNITY_PM << " and C.AppStatus = 1";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* temp_conn = conn.get();
    if (NULL == temp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    CRldbQuery query(temp_conn);
    query.Query(stream_sql.str());

    ResidentPerAccount account;
    while (query.MoveToNextRow())
    {
        memset(&account, 0, sizeof(account));
        Snprintf(account.name, sizeof(account.name),  dbinterface::DataConfusion::Decrypt(query.GetRowData(0)).c_str());
        Snprintf(account.sip_account, sizeof(account.sip_account), query.GetRowData(1));
        Snprintf(account.room_number, sizeof(account.room_number), query.GetRowData(2));
        account.ip_direct =  ATOI(query.GetRowData(3));
        Snprintf(account.phone, sizeof(account.phone),  dbinterface::DataConfusion::Decrypt(query.GetRowData(4)).c_str());
        account.id =  ATOI(query.GetRowData(5));
        account.role =  ATOI(query.GetRowData(6));
        Snprintf(account.phone_code, sizeof(account.phone_code), query.GetRowData(8));
        Snprintf(account.phone2, sizeof(account.phone2),  dbinterface::DataConfusion::Decrypt(query.GetRowData(9)).c_str());
        Snprintf(account.phone3, sizeof(account.phone3),  dbinterface::DataConfusion::Decrypt(query.GetRowData(10)).c_str());
        account.phone_status =  ATOI(query.GetRowData(11));
        account.only_apt =  ATOI(query.GetRowData(12));
        account.cnf.call_type = ATOI(query.GetRowData(13));
        Snprintf(account.uuid, sizeof(account.uuid), query.GetRowData(14));
        Snprintf(account.user_info_uuid, sizeof(account.user_info_uuid), query.GetRowData(15));
        Snprintf(account.account, sizeof(account.account), query.GetRowData(16));
        account_list.push_back(account);
    }

    ReleaseDBConn(conn);
    return 0;
}

int ResidentPersonalAccount::GetNodesByUserInfoUUID(const std::string &userinfo_uuid, PersonalAccountNodeInfoMap& nodes)
{
    if(userinfo_uuid.size() == 0)
    {
        return 0;
    }
    std::stringstream sql;
    sql << "select P1.Account,P1.Role,P1.Name,P2.Account from PersonalAccount P1 left join PersonalAccount P2 on P2.UUID=P1.ParentUUID "
        << "where P1.UserInfoUUID = '"
        << userinfo_uuid
        << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    while (query.MoveToNextRow())
    {
        PersonalAccountNodeInfo node_info;
        std::string account = query.GetRowData(0);
        //主账号的node和account相同
        std::string node = query.GetRowData(0);
        int role = ATOI(query.GetRowData(1));
        std::string username =  dbinterface::DataConfusion::Decrypt(query.GetRowData(2)).c_str();
        //从账号node为主账号account
        if (role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT 
            || role == ACCOUNT_ROLE_PERSONNAL_ATTENDANT)
        {
            node = query.GetRowData(3);
        }

        node_info.conn_type = ChangeRoleToConnType(role);
        Snprintf(node_info.node, sizeof(node_info.node), node.c_str());
        Snprintf(node_info.username, sizeof(node_info.username), username.c_str());
        nodes.insert(std::make_pair(account, node_info));
    }
    
    ReleaseDBConn(conn);
    return 0;
}

void ResidentPersonalAccount::UpdateLanguageByUserInfoUUID(const std::string &language, const std::string &userinfo_uuid)
{
    std::stringstream sql;
    sql << "update PersonalAccount set Language = '"  << language << "' where UserInfoUUID = '" << userinfo_uuid << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return;
    }
    
    conn->Execute(sql.str());
    ReleaseDBConn(conn);

    return;
}

int ResidentPersonalAccount::GetMainSiteAccountByUserInfoUUID(const std::string &userinfo_uuid, ResidentPerAccount& account)
{
    PerAccountUserInfo userinfo;
    UserInfoAccount account_info;
    if (0 == dbinterface::PersonalAccountUserInfo::GetPerAccountInfoByUUID(userinfo_uuid, userinfo))
    {
        return GetUidAccount(userinfo.main_user_account, account);
    }
    else if (0 == dbinterface::AccountUserInfo::GetAccountUserInfoByUUID(userinfo_uuid, account_info))
    {
        //pm app才需要去查AccountUserInfo
        return GetUidAccount(account_info.main_user_account, account);
    }

    return -1;
}


// 获取PersonalAccount和PersonalAccountUserInfo两张表的信息 
int ResidentPersonalAccount::GetUserInfoByAccount(const std::string &account, ResidentPerAccount& account_info)
{
    if (0 == dbinterface::ResidentPersonalAccount::GetUidAccount(account, account_info))
    {

        return 0;

    }
    return -1;
}

// 获取从账号UserInfo列表
int ResidentPersonalAccount::GetAttendantUserInfoListByNode(const std::string& node_account, ResidentPerAccountList& attendant_list)
{
    ResidentPerAccount node_info;
    memset(&node_info, 0, sizeof(node_info));
    if (0 == dbinterface::ResidentPersonalAccount::GetUidAccount(node_account, node_info))
    {
        if (0 == dbinterface::ResidentPersonalAccount::GetAttendantUserInfoListByParentUUID(node_info.uuid, attendant_list))
        {
            return 0;
        }
    }
    return -1;
}

/*  不同位置的设备同步不同的时区设置，具体如下,
    社区设备同步对应 Community 的 TimeZone, 
    个人公共设备同步对应 Installer 的 TimeZone, 
    个人所属设备同步 EndUser 的 TimeZone;              */
int ResidentPersonalAccount::GetDevTimeZoneConfig(const short grade, const uint mng_id, const int flag ,const std::string& node, std::string& time_zone, int& time_format)
{
    std::stringstream sql;
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    if ((grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC) 
     || (grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
     || (grade == csmain::COMMUNITY_DEVICE_TYPE_PERSONAL))//社区
    {
        sql << "select TimeZone,CustomizeForm from Account where ID = " << mng_id;
    }
    else if (flag & DEVICE_SETTING_FLAG_PER_PUBLIC)//个人公共
    {
        sql << "select A.TimeZone,A.CustomizeForm from Account A left join PersonalAccount P on A.ID = P.ParentID where P.Account = '" << node << "' and role=12";
    }
    else    //个人云
    {
        sql << "select TimeZone,CustomizeForm from PersonalAccount where Account = '" << node << "'";
    }

    CRldbQuery query(conn.get());
    query.Query(sql.str());
    if (query.MoveToNextRow())
    {
        time_zone = query.GetRowData(0);
        time_format = ATOI(query.GetRowData(1));
    }

    ReleaseDBConn(conn);
    return 0;
}

int ResidentPersonalAccount::GetNfcCodeByAccount(const std::string &account, std::string &nfccode)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    std::stringstream sql;
    sql << "select NFCCode from PersonalAccount where account = '" << account << "'";

    CRldbQuery query(tmp_conn);
    query.Query(sql.str());

    if(query.MoveToNextRow())
    {
        nfccode = query.GetRowData(0);
    }

    ReleaseDBConn(conn);
    return 0;
}

int ResidentPersonalAccount::GetAlarmReminderStatusByAccount(const std::string& account, int &alarm_reminder_status)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    std::stringstream sql;
    sql << "select EnableStrongAlarm from PersonalAccount WHERE account = '"<< account << "'";

    CRldbQuery query(tmp_conn);
    query.Query(sql.str());

    if(query.MoveToNextRow())
    {
        alarm_reminder_status = ATOI(query.GetRowData(0));
    }

    ReleaseDBConn(conn);
    return 0;
}

int ResidentPersonalAccount::GetNodeListByProjectUUID(const std::string& project_uuid, std::set<std::string>& node_list)
{
    std::stringstream sql;
    sql << "select Account from PersonalAccount where ParentUUID = '" << project_uuid << "'"
          << "And Role =" << ACCOUNT_ROLE_COMMUNITY_MAIN;
    
    GET_DB_CONN_ERR_RETURN(conn, -1);
    CRldbQuery query(conn.get());
    query.Query(sql.str());

    while (query.MoveToNextRow())
    {
        node_list.insert(query.GetRowData(0));
    }

    return 0;
}

std::string ResidentPersonalAccount::GetAccountCurrentTimeString(const std::string& account, const std::map<string, AKCS_DST>& timezone_config)
{
    std::string node;
    //获取主账号失败 返回空
    if (0 != GetNodeByAccount(account, node))
    {
        return "";
    }
    std::string timezone;
    timezone = GetNodeTimeZoneStr(node);

    return GetNodeNowDateTimeByTimeZoneStr(timezone, timezone_config);
}

std::string ResidentPersonalAccount::GetAccountTimeStringByTimestamp(const std::string& account, int64_t timestamp, const std::map<std::string, AKCS_DST>& timezone_config)
{
    std::string node;
    //获取主账号失败 返回空
    if (0 != GetNodeByAccount(account, node))
    {
        return "";
    }

    std::string timezone;
    timezone = GetNodeTimeZoneStr(node);

    return GetDateTimeByTimeZoneStr(timestamp, timezone, timezone_config);
}


int ResidentPersonalAccount::GetNodeByAccount(const std::string& account, std::string& node)
{
    ResidentPerAccount per_account;
    memset(&per_account, 0, sizeof(per_account));
    if (0 != GetUidAccount(account, per_account))
    {
        return -1;
    }
    
    // 若为主账号 则node为account
    if (akjudge::IsNodeAccountRole(per_account.role))
    {
        node = account;
        return 0;
    }
    
    //社区或单住户从账号 则node为主账号的account
    if (per_account.role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT || per_account.role == ACCOUNT_ROLE_PERSONNAL_ATTENDANT)
    {
        if (0 != GetAccountByUUID(per_account.parent_uuid, node))
        {
            return -1;
        }
        return 0;
    }
    return -1;
}

int ResidentPersonalAccount::GetNickNameAndNodeAndMngIDByUid(const std::string& uid, std::string& name, std::string& node, int& manager_id)
{
    ResidentPerAccount account;
    memset(&account, 0, sizeof(account));
    ResidentPerAccount main_account;
    memset(&main_account, 0, sizeof(main_account));
    if (0 == dbinterface::ResidentPersonalAccount::GetUidAccount(uid, account))
    {
        if (account.role == ACCOUNT_ROLE_PERSONNAL_MAIN 
            || account.role == ACCOUNT_ROLE_COMMUNITY_MAIN 
            || account.role == ACCOUNT_ROLE_COMMUNITY_PM)
        {
            node = uid;
            name = account.name;
            manager_id = account.parent_id;
        }
        else if (0 == dbinterface::ResidentPersonalAccount::GetUUIDAccount(account.parent_uuid, main_account))
        {
            name = account.name;
            node = main_account.account;
            manager_id = main_account.parent_id;
        }
    }
    else
    {
        return -1;
    }

    return 0;
}

void GetResidentResidentNodeDbDeliveryUUID(const ResidentPerAccount &account, std::string &db_delivery_uuid)
{
    if (account.role == ACCOUNT_ROLE_COMMUNITY_MAIN 
        || account.role == ACCOUNT_ROLE_COMMUNITY_PM)
    {
        db_delivery_uuid = account.parent_uuid; //项目uuid
    }
    else if (account.role == ACCOUNT_ROLE_PERSONNAL_MAIN)
    {
        db_delivery_uuid = account.uuid;//单住户主账号uuid
    }    
}

int ResidentPersonalAccount::GetNickNameAndNodeByUid(const std::string& uid, std::string& name, 
    std::string& node, std::string &db_delivery_uuid, std::string &projectuuid, int &mng_id)
{
    ResidentPerAccount account;
    memset(&account, 0, sizeof(account));
    ResidentPerAccount main_account;
    memset(&main_account, 0, sizeof(main_account));
    if (0 == dbinterface::ResidentPersonalAccount::GetUidAccount(uid, account))
    {
        if (account.role == ACCOUNT_ROLE_PERSONNAL_MAIN 
            || account.role == ACCOUNT_ROLE_COMMUNITY_MAIN 
            || account.role == ACCOUNT_ROLE_COMMUNITY_PM)
        {
            name = account.name;
            node = uid;
            GetResidentResidentNodeDbDeliveryUUID(account, db_delivery_uuid);
            if (account.role != ACCOUNT_ROLE_PERSONNAL_MAIN)//单住户不设置项目uuid
            {
                projectuuid = account.parent_uuid;
                mng_id = account.parent_id;
            }
        }
        else if (0 == dbinterface::ResidentPersonalAccount::GetUUIDAccount(account.parent_uuid, main_account))
        {
            name = account.name;
            node = main_account.account;
            if (main_account.role != ACCOUNT_ROLE_PERSONNAL_MAIN)//单住户不设置项目uuid
            {
                projectuuid = main_account.parent_uuid;
                mng_id = main_account.parent_id;
            }
            GetResidentResidentNodeDbDeliveryUUID(main_account, db_delivery_uuid);
        }
    }
    else
    {
        return -1;
    }

    return 0;
} 


int ResidentPersonalAccount::GetCommunityAccountByProjectID(uint32_t project_id, 
 ResidentPerAccountMap &community_main_account_list, ResidentPerAccountSlaveMap &community_slave_account_list, MapUnitCommunitAccountList &unit_main_map,
 ResidentUUIDPerAccountMap &community_uuid_account_list)
{
    std::stringstream sql;
    sql << "select "<< account_sec  << " From PersonalAccount P left join Account A on A.UUID=P.ParentUUID \
        where A.ID="
        << project_id << " and P.Role=" << ACCOUNT_ROLE_COMMUNITY_MAIN;
    
    GET_DB_CONN_ERR_RETURN(conn, -1);
    CRldbQuery query(conn.get());
    query.Query(sql.str());
    while (query.MoveToNextRow())
    {
        ResidentPerAccount account;
        GetAccountFromSql(account, query);
        community_main_account_list.insert(std::make_pair(account.account, account));
        community_uuid_account_list.insert(std::make_pair(account.uuid, account));

        COMMUNITY_ACCOUNTS_INFO account_info;
        account_info.unit_id = account.unit_id;
        Snprintf(account_info.account, sizeof(account_info.account), account.account);
        Snprintf(account_info.uuid, sizeof(account_info.uuid), account.uuid);

        auto it = unit_main_map.find(account.unit_id);
        if (it != unit_main_map.end()) {
            it->second.push_back(account_info);
        } else {
            CommunitAccountInfoList new_list = {account_info};
            unit_main_map.insert({account.unit_id, new_list});
        }      
    }

    std::stringstream sql2;
    sql2 << "select "<< account_sec  << " From PersonalAccount P left join PersonalAccount PP on PP.UUID=P.ParentUUID \
        left join Account A on A.UUID=PP.ParentUUID \
        where A.ID="
        << project_id << " and P.Role=" << ACCOUNT_ROLE_COMMUNITY_ATTENDANT;

    query.Query(sql2.str());
    while (query.MoveToNextRow())
    {
        ResidentPerAccount account;
        GetAccountFromSql(account, query);
        
        community_slave_account_list.insert(std::make_pair(account.parent_uuid, account));        
        community_uuid_account_list.insert(std::make_pair(account.uuid, account));
    }
    return 0;
}


int ResidentPersonalAccount::UpdateNodeTimeZoneByUUID(const std::string& node_uuid, const std::string& timezone)
{
    std::stringstream stream_sql;
    stream_sql << "update PersonalAccount set TimeZone = '" << timezone << "' where UUID = '" << node_uuid << "'";

    GET_DB_CONN_ERR_RETURN(conn, -1);

    conn->Execute(stream_sql.str());

    return 0;
}

void ResidentPersonalAccount::GetEmailInfoByAccount(EmailInfo& email_info, const std::string& user)
{
    ResidentPerAccount personal_account;
    memset(&personal_account, 0, sizeof(personal_account));
    
    if (0 == GetUserAccountFromMaster(user, personal_account))
    {
        Snprintf(email_info.username, sizeof(email_info.username), personal_account.name);
        email_info.role = personal_account.role;

        dbinterface::AccountInfo account;
        if (ACCOUNT_ROLE_COMMUNITY_MAIN == email_info.role || ACCOUNT_ROLE_COMMUNITY_PM == email_info.role)
        {
            dbinterface::Account::GetAccountByUUID(personal_account.parent_uuid, account);
        }
        else if (ACCOUNT_ROLE_COMMUNITY_ATTENDANT == email_info.role)
        {
            ResidentPerAccount main_account;
            memset(&main_account, 0, sizeof(main_account));
            if (0 == GetUUIDAccount(personal_account.parent_uuid, main_account))
            {
                dbinterface::Account::GetAccountByUUID(main_account.parent_uuid, account);
            }
        }
        
        Snprintf(email_info.community, sizeof(email_info.community), account.location);

        // 获取dis所属的oem
        DistributorInfoSt dis_info;
        if (0 == dbinterface::DistributorInfo::GetDistributorInfoByPersonalAccount(personal_account, dis_info))
        {
            Snprintf(email_info.oem_name, sizeof(email_info.oem_name), dis_info.oem_name);
        }
    }
}

bool ResidentPersonalAccount::IsCommunityAptEnableSmartHome(const ResidentPerAccount& account)
{
    std::string node;
    if (account.role == ACCOUNT_ROLE_COMMUNITY_MAIN)
    {
        node = account.account;
    }
    else if (account.role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT)
    {
        ResidentPerAccount node_account;
        dbinterface::ResidentPersonalAccount::GetUUIDAccount(account.parent_uuid, node_account);
        node = node_account.account;
    }
    
    PersonalAccountCnfInfo node_cnf;
    if (0 == dbinterface::PersonalAccountCnf::GetPeronalAccountCnfByNode(node, node_cnf))
    {
        return node_cnf.enable_smarthome;
    }
    return false;
}

int ResidentPersonalAccount::GetNodeUUIDByAccount(const std::string& account, std::string& node_uuid)
{
    ResidentPerAccount per_account;
    memset(&per_account, 0, sizeof(per_account));
    if (0 != GetUidAccount(account, per_account))
    {
        return -1;
    }
    
    // 若为主账号 则node为account
    if (akjudge::IsNodeAccountRole(per_account.role))
    {
        node_uuid = per_account.uuid;
        return 0;
    }

    //社区或单住户从账号 则node为主账号的account
    if (per_account.role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT || per_account.role == ACCOUNT_ROLE_PERSONNAL_ATTENDANT)
    {
        std::string node;
        if (0 != GetAccountByUUID(per_account.parent_uuid, node))
        {
            return -1;
        }
        node_uuid = per_account.parent_uuid;
        return 0;
    }
    
    return -1;
}

}
