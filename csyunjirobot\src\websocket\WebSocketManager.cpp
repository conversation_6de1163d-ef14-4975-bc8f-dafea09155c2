#include "WebSocketManager.h"
#include <chrono>
#include <random>
#include <sstream>
#include <json/json.h>

/**
 * @brief 获取管理器状态信息
 * @details 返回包含所有客户端状态的JSON格式字符串
 * @return JSON格式的状态信息
 */
std::string YunJiWebSocketManager::GetStatusInfo() const {
    Json::Value status;
    Json::Value clients_array(Json::arrayValue);

    {
        std::lock_guard<std::mutex> lock(clients_mutex_);

        status["manager_running"] = is_running_.load();
        status["heartbeat_interval"] = heartbeat_interval_;
        status["total_clients"] = static_cast<int>(clients_.size());

        int active_count = 0;
        for (const auto& pair : clients_) {
            const auto& client_info = pair.second;

            Json::Value client_status;
            client_status["client_id"] = client_info->client_id;
            client_status["url"] = client_info->url;
            client_status["is_active"] = client_info->is_active;

            if (client_info->client) {
                client_status["is_connected"] = client_info->client->GetIsConnected();
            } else {
                client_status["is_connected"] = false;
            }

            // 计算距离上次心跳的时间
            auto now = std::chrono::steady_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::seconds>(now - client_info->last_heartbeat);
            client_status["seconds_since_last_heartbeat"] = static_cast<int>(duration.count());

            clients_array.append(client_status);

            if (client_info->is_active) {
                active_count++;
            }
        }

        status["active_clients"] = active_count;
    }

    status["clients"] = clients_array;

    Json::FastWriter writer;
    std::string result = writer.write(status);

    // 移除末尾换行符
    if (!result.empty() && result.back() == '\n') {
        result.pop_back();
    }

    return result;
}