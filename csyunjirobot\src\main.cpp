#include <memory>
#include <thread>
#include <chrono>
#include <atomic>
#include "Metric.h"
#include "AES256.h"
#include "CachePool.h"
#include "AkcsAppInit.h"
#include "EtcdCliMng.h"
#include "NotifyMsgControl.h"
#include "YunJiRobotConfInit.h"
#include "ConfigFileReader.h"
#include "ConnectionPool.h"
#include "YunJiRobotEtcd.h"
#include "YunJiRobotConfig.h"
#include "AkcsDnsResolver.h"
#include "http/HttpServer.h"
#include "YunJiRobotRpcServer.h"
#include "websocket/WebSocketManager.h"
#include "dbinterface/YunJiRobotInfo.h"
#include "YunJiRobotDefine.h"

YUNJI_ROBOT_CONFIG g_yunji_robot_config;

// 全局 WebSocket 管理器
std::unique_ptr<YunJiWebSocketManager> g_websocket_manager;

// 获取WebSocket管理器实例的全局函数
YunJiWebSocketManager* GetWebSocketManager()
{
    return g_websocket_manager.get();
}

void InstanceInit()
{
    CacheManager::getInstance()->Init(PROCESS_REDIS_CONF_FILE, "csyunjirobotCacheInstances");

    GetNotifyMsgControlInstance()->Init();
}

// WebSocket管理器初始化函数
void WebSocketManagerInit()
{
    AK_LOG_INFO << "初始化WebSocket管理器...";

    // 创建WebSocket管理器实例，使用配置文件中的心跳间隔
    g_websocket_manager = std::unique_ptr<YunJiWebSocketManager>(new YunJiWebSocketManager(g_yunji_robot_config.websocket_heartbeat_interval));

    // 设置连接状态变化回调
    g_websocket_manager->SetConnectionStateCallback([](const std::string& client_id, bool connected) {
        if (connected) {
            AK_LOG_INFO << "WebSocket客户端 [" << client_id << "] 连接成功";
        } else {
            AK_LOG_WARN << "WebSocket客户端 [" << client_id << "] 连接断开";
        }
    });

    // 设置客户端错误回调
    g_websocket_manager->SetClientErrorCallback([](const std::string& client_id, const std::string& error) {
        AK_LOG_ERROR << "WebSocket客户端 [" << client_id << "] 发生错误: " << error;
    });

    // 启动WebSocket管理器
    g_websocket_manager->Start();

    // 添加WebSocket客户端
    YunJiRobotInfoList yunji_robot_info_list;
    dbinterface::YunJiRobotInfo::GetYunJiRobotInfoList(yunji_robot_info_list);

    for (const auto& yunji_robot_info : yunji_robot_info_list) 
    {
        if (yunji_robot_info.is_enable && strlen(yunji_robot_info.access_key_id) > 0 && strlen(yunji_robot_info.access_key_secret) > 0)
        {
            bool success = g_websocket_manager->AddClient(yunji_robot_info.account_uuid, g_yunji_robot_config.websocket_url, 
                        yunji_robot_info.access_key_id, yunji_robot_info.access_key_secret);

            if (success) {
                AK_LOG_INFO << "添加WebSocket客户端成功, account_uuid=" << yunji_robot_info.account_uuid;
                g_websocket_manager->ConnectClient(yunji_robot_info.account_uuid);
            } else {
                AK_LOG_ERROR << "添加WebSocket客户端失败";
            }
        }
    }
}

int main(int argc, char* argv[])
{
    if (!IsSingleton2("/var/run/csyunjirobot.pid"))
    {
        printf("another csyunjirobot has been running in this system.\n") ;
        return -1;
    }
    
    GlogInit2(argv[0], "csyunjirobotlog");

    ConfigInit();
    
    EtcdConnInit();
    
    InstanceInit();

    if (DaoInit() != 0)
    {
        AK_LOG_FATAL << "DaoInit fialed.";
        return -1;
    }

    // init etcd register loop.
    std::thread etcd_cli_thread = std::thread(EtcdSrvInit);

    std::thread rpc_server_thread = std::thread(RpcServerInit);

    std::thread http_thread(StartHttpServer);

    WebSocketManagerInit();

    InitMetricInstance();

    AK_LOG_INFO << "csyunjirobot is running with WebSocket manager.";

    http_thread.join();
    etcd_cli_thread.join();
    rpc_server_thread.join();

    GlogClean2();
    return 0;
}