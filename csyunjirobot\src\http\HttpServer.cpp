#include "AkLogging.h"
#include "HttpServer.h"
#include "HttpMessage.h"
#include "HttpHandler.h"
#include "YunJiRobotConfig.h"

extern YUNJI_ROBOT_CONFIG g_yunji_robot_config;
static HTTPAllRespCallbackMap g_http_response_callbacks;

// 当请求无效时的处理函数
void DefaultHandler(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_WARN << "http req route is not define, http req is [ " << ctx->original_uri() << " ], remote_ip is " << ctx->remote_ip();
    LOG_INFO << "http req type is [ " << ctx->original_type() << " ]";
    ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
    cb(BuildHttpErrorMessage(ERR_CODE_INVALID_REQUEST_ROUTE));
}

void HttpMetricsCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    HTTPRespCallback handler = g_http_response_callbacks[HTTP_ROUTE::ON_METRICS];
    if (handler)
    {
        handler(ctx, cb);
    }
    return;
}

void StartHttpServer()
{
    bool ipv6 = false;
    const int port = g_yunji_robot_config.http_port;
    const int thread_num = g_yunji_robot_config.http_thread_num;
    g_http_response_callbacks = HTTPAllRespMapInit();

    evpp::http::Server server(thread_num, ipv6);
    server.RegisterDefaultHandler(&DefaultHandler);

    // metrics监控指标
    server.RegisterHandler("/metrics", HttpMetricsCallback);

    server.Init(port);
    server.Start();
    return;
}