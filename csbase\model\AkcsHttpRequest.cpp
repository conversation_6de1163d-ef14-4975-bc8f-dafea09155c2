#include "AkcsHttpRequest.h"
#include "AkLogging.h"
#include <iostream>
#include <curl/curl.h>
#include <curl/types.h>
#include <curl/easy.h>
#include <sstream>
#include <stdlib.h>
#include <string.h>
#include "encrypt/Base64.h"
#include "AkcsMonitor.h"

using namespace model;

HttpRequest& HttpRequest::GetInstance()
{
	static HttpRequest request;
	return request;
}

static void SendHttpConnErrorAlarm(const std::string &url)
{
    char error[1024] = "";
    snprintf(error, sizeof(error), "http request %s error", url.c_str());
    
    std::string worker_node = "http_request";
    int ret = AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm(worker_node, error, AKCS_MONITOR_ALARM_HTTP_REQUEST);
    if (ret == -1)
    {
        AK_LOG_WARN << "SystemMonitor has not been init.";
    }
}

size_t WriteMemoryCallback(void *ptr, size_t size, size_t nmemb, void *data)
{
    size_t realsize = size * nmemb;
    struct MemoryStruct *mem = (struct MemoryStruct *)data;

    mem->memory = (char *)realloc(mem->memory, mem->size + realsize + 1);
    if (mem->memory)
    {
        memcpy(&(mem->memory[mem->size]), ptr, realsize);
        mem->size += realsize;
        mem->memory[mem->size] = 0;
    }

    return realsize;
}

int HttpRequest::Get(const std::string &urlhead, const HttpRespuestKV &kv,  std::string &respone)
{
    int ret = -1;
    CURLcode res = curl_global_init(CURL_GLOBAL_ALL);
    if(CURLE_OK != res)
    {
        AK_LOG_WARN << "curl init failed";
        return -1;
    }

    CURL *pcurl = NULL;
    pcurl = curl_easy_init();
    if( NULL == pcurl)
    {
        AK_LOG_WARN << "Init CURL failed...";
        return -1;
    }

    std::stringstream url;
    url << urlhead << "?";
    for (const auto& tmpkv : kv)
    {
        url << "&" << tmpkv.first  << "=" << tmpkv.second;
    }

    AK_LOG_INFO << "http get:" << url.str();

    curl_easy_setopt(pcurl, CURLOPT_TIMEOUT, 5L);//请求超时时长
    curl_easy_setopt(pcurl, CURLOPT_CONNECTTIMEOUT, 3L); //连接超时时长
    curl_easy_setopt(pcurl, CURLOPT_SSL_VERIFYPEER, 0L); //去掉证书认证
    curl_easy_setopt(pcurl, CURLOPT_SSL_VERIFYHOST, false);
    curl_easy_setopt(pcurl, CURLOPT_HEADER, 0L);  //若启用，会将头文件的信息作为数据流输出
    curl_easy_setopt(pcurl, CURLOPT_WRITEFUNCTION, WriteMemoryCallback);  //得到请求结果后的回调函数

    MemoryStruct data_trunk;  //请求结果的保存格式
    curl_easy_setopt(pcurl, CURLOPT_WRITEDATA, &data_trunk);

    curl_easy_setopt(pcurl, CURLOPT_NOSIGNAL, 1L); //关闭中断信号响应
    curl_easy_setopt(pcurl, CURLOPT_VERBOSE, 1L); //启用时会汇报所有的信息
    curl_easy_setopt(pcurl, CURLOPT_URL, url.str().c_str() ); //需要获取的URL地址


    std::string client_id_secret = HTTP_CLIENT_ID_SECRET;
    int client_len = client_id_secret.size();
    int base_out_len = (client_len/3 + 1 ) * 4 + 1;
    char *base_out = (char *)malloc(base_out_len);
    if (!base_out)
    {
        AK_LOG_WARN << "Memory alloc failed.size=" << base_out_len;
        return -2;
    }
    memset(base_out, 0, base_out_len);
    Base64Encode(client_id_secret.c_str(), client_len, base_out, &base_out_len);

    std::string auth = "Authorization:";
    auth += base_out;
    free(base_out);

    curl_slist *slist = NULL;
    //slist = curl_slist_append(slist,"Accept-Encoding:gzip, deflate, sdch");
    slist = curl_slist_append(slist,"Connection:keep-alive");
    slist = curl_slist_append(slist, HTTP_HEAD_API_VERSION.c_str());
    slist = curl_slist_append(slist, auth.c_str());
    slist = curl_slist_append(slist, "Content-Type:application/x-www-form-urlencoded");
    curl_easy_setopt(pcurl, CURLOPT_HTTPHEADER, slist);

    res = curl_easy_perform(pcurl);  //执行请求

    long res_code=0;
    res = curl_easy_getinfo(pcurl, CURLINFO_RESPONSE_CODE, &res_code);

    //正确响应后，请请求转写成本地文件的文件
    if(( res == CURLE_OK ) && (res_code == 200 || res_code == 201))
    {
        respone = data_trunk.memory;
        ret = 0;
    }

    curl_slist_free_all(slist);
    curl_easy_cleanup(pcurl);
    curl_global_cleanup();
    return ret;
}

int HttpRequest::Post(const std::string &url, const std::string &data,  std::string &respone, int data_type)
{
    int ret = -1;
    CURLcode res = curl_global_init(CURL_GLOBAL_ALL);
    if(CURLE_OK != res)
    {
        AK_LOG_WARN << "curl init failed";
        return -1;
    }

    CURL *pcurl = NULL;
    pcurl = curl_easy_init();
    if( NULL == pcurl)
    {
        AK_LOG_WARN << "Init CURL failed...";
        return -1;
    }

    AK_LOG_INFO << "http post:" << url << " datas:" << data;

    curl_easy_setopt(pcurl, CURLOPT_CUSTOMREQUEST, "POST");
    curl_easy_setopt(pcurl, CURLOPT_POST, 1);
    curl_easy_setopt(pcurl, CURLOPT_SSL_VERIFYPEER, 0L); //去掉证书认证
    curl_easy_setopt(pcurl, CURLOPT_SSL_VERIFYHOST, false);
    curl_easy_setopt(pcurl, CURLOPT_TIMEOUT, 5L);//请求超时时长
    curl_easy_setopt(pcurl, CURLOPT_CONNECTTIMEOUT, 3L); //连接超时时长
    curl_easy_setopt(pcurl, CURLOPT_HEADER, 0L);  //若启用，会将头文件的信息作为数据流输出
    curl_easy_setopt(pcurl, CURLOPT_WRITEFUNCTION, WriteMemoryCallback);  //得到请求结果后的回调函数
    curl_easy_setopt(pcurl, CURLOPT_POSTFIELDS, data.c_str());


    MemoryStruct data_trunk;  //请求结果的保存格式
    curl_easy_setopt(pcurl, CURLOPT_WRITEDATA, &data_trunk);

    curl_easy_setopt(pcurl, CURLOPT_NOSIGNAL, 1L); //关闭中断信号响应
    curl_easy_setopt(pcurl, CURLOPT_VERBOSE, 1L); //启用时会汇报所有的信息
    curl_easy_setopt(pcurl, CURLOPT_URL, url.c_str() ); //需要获取的URL地址

    std::string client_id_secret = HTTP_CLIENT_ID_SECRET;
    int client_len = client_id_secret.size();
    int base_out_len = (client_len/3 + 1 ) * 4 + 1;
    char *base_out = (char *)malloc(base_out_len);
    if (!base_out)
    {
        AK_LOG_WARN << "Memory alloc failed.size=" << base_out_len;
        return -2;
    }
    memset(base_out, 0, base_out_len);
    Base64Encode(client_id_secret.c_str(), client_len, base_out, &base_out_len);

    std::string auth = "Authorization:";
    auth += base_out;
    free(base_out);

    curl_slist *slist = NULL;
    //slist = curl_slist_append(slist,"Accept-Encoding:gzip, deflate, sdch");
    slist = curl_slist_append(slist,"Connection:keep-alive");
    slist = curl_slist_append(slist, auth.c_str());
    if(HTTP_DATA_TYPE_JSON == data_type)
    {
        slist = curl_slist_append(slist,"Content-Type:application/json");
    }
    else
    {
        slist = curl_slist_append(slist, "Content-Type:application/x-www-form-urlencoded");
    }
    curl_easy_setopt(pcurl, CURLOPT_HTTPHEADER, slist);

    res = curl_easy_perform(pcurl);  //执行请求
    if (res != CURLE_OK)
    {
        const char *error_desc= curl_easy_strerror(res);
        AK_LOG_INFO << "Post Failed, res=" << res << ";error_desc=" << error_desc;
    }

    //正确响应后，请请求转写成本地文件的文件
    long res_code=0;
    res = curl_easy_getinfo(pcurl, CURLINFO_RESPONSE_CODE, &res_code);
    if(( res == CURLE_OK ) && (res_code == 200 || res_code == 201))
    {
        respone = data_trunk.memory;
        AK_LOG_INFO << "Post Success, reponse=" << respone;
        ret = 0;
    }
    else
    {
        AK_LOG_INFO << "Post Failed, res=" << res << ";res_code=" << res_code;
        SendHttpConnErrorAlarm(url);
    }

    curl_slist_free_all(slist);
    curl_easy_cleanup(pcurl);
    curl_global_cleanup();
    return ret;
}

int HttpRequest::Delete(const std::string &url, const std::vector<std::string> &headers, const std::string &body, std::string &response)
{
    int ret = -1;
    CURLcode res = curl_global_init(CURL_GLOBAL_ALL);
    if(CURLE_OK != res)
    {
        AK_LOG_WARN << "curl init failed";
        return -1;
    }

    CURL *pcurl = NULL;
    pcurl = curl_easy_init();
    if( NULL == pcurl)
    {
        AK_LOG_WARN << "Init CURL failed...";
        return -1;
    }

    AK_LOG_INFO << "http delete:" << url << " body:" << body;

    // 设置DELETE方法
    curl_easy_setopt(pcurl, CURLOPT_CUSTOMREQUEST, "DELETE");
    curl_easy_setopt(pcurl, CURLOPT_SSL_VERIFYPEER, 0L); //去掉证书认证
    curl_easy_setopt(pcurl, CURLOPT_SSL_VERIFYHOST, false);
    curl_easy_setopt(pcurl, CURLOPT_TIMEOUT, 30L);//请求超时时长
    curl_easy_setopt(pcurl, CURLOPT_CONNECTTIMEOUT, 10L); //连接超时时长
    curl_easy_setopt(pcurl, CURLOPT_HEADER, 0L);  //若启用，会将头文件的信息作为数据流输出
    curl_easy_setopt(pcurl, CURLOPT_WRITEFUNCTION, WriteMemoryCallback);  //得到请求结果后的回调函数
    
    // 如果有body数据需要发送，设置请求体
    if (!body.empty())
    {
        curl_easy_setopt(pcurl, CURLOPT_POSTFIELDS, body.c_str());
    }

    MemoryStruct data_trunk;  //请求结果的保存格式
    curl_easy_setopt(pcurl, CURLOPT_WRITEDATA, &data_trunk);

    curl_easy_setopt(pcurl, CURLOPT_NOSIGNAL, 1L); //关闭中断信号响应
    curl_easy_setopt(pcurl, CURLOPT_VERBOSE, 1L); //启用时会汇报所有的信息
    curl_easy_setopt(pcurl, CURLOPT_URL, url.c_str() ); //需要获取的URL地址

    // 设置自定义HTTP头
    curl_slist *slist = NULL;
    for (const auto& header : headers)
    {
        slist = curl_slist_append(slist, header.c_str());
    }
    
    // 如果用户没有提供Connection头，默认添加
    bool has_connection = false;
    for (const auto& header : headers)
    {
        if (header.find("Connection:") == 0)
        {
            has_connection = true;
            break;
        }
    }
    if (!has_connection)
    {
        slist = curl_slist_append(slist, "Connection:keep-alive");
    }
    
    curl_easy_setopt(pcurl, CURLOPT_HTTPHEADER, slist);

    res = curl_easy_perform(pcurl);  //执行请求
    if (res != CURLE_OK)
    {
        const char *error_desc= curl_easy_strerror(res);
        AK_LOG_INFO << "Delete Failed, res=" << res << ";error_desc=" << error_desc;
    }

    //检查响应状态码
    long res_code=0;
    res = curl_easy_getinfo(pcurl, CURLINFO_RESPONSE_CODE, &res_code);
    if(( res == CURLE_OK ) && (res_code >= 200 && res_code < 300))
    {
        response = data_trunk.memory;
        AK_LOG_INFO << "Delete Success, response=" << response;
        ret = 0;
    }
    else
    {
        AK_LOG_INFO << "Delete Failed, res=" << res << ";res_code=" << res_code;
        SendHttpConnErrorAlarm(url);
    }

    curl_slist_free_all(slist);
    curl_easy_cleanup(pcurl);
    curl_global_cleanup();
    return ret;
}


int HttpRequest::PostWithHeaders(const std::string &url, const std::vector<std::string> &headers, 
        const std::string &data,  std::string &respone, int data_type)
{
    int ret = -1;
    CURLcode res = curl_global_init(CURL_GLOBAL_ALL);
    if(CURLE_OK != res)
    {
        AK_LOG_WARN << "curl init failed";
        return -1;
    }

    CURL *pcurl = NULL;
    pcurl = curl_easy_init();
    if( NULL == pcurl)
    {
        AK_LOG_WARN << "Init CURL failed...";
        return -1;
    }

    AK_LOG_INFO << "http post:" << url << " datas:" << data;

    curl_easy_setopt(pcurl, CURLOPT_CUSTOMREQUEST, "POST");
    curl_easy_setopt(pcurl, CURLOPT_POST, 1);
    curl_easy_setopt(pcurl, CURLOPT_SSL_VERIFYPEER, 0L); //去掉证书认证
    curl_easy_setopt(pcurl, CURLOPT_SSL_VERIFYHOST, false);
    curl_easy_setopt(pcurl, CURLOPT_TIMEOUT, 5L);//请求超时时长
    curl_easy_setopt(pcurl, CURLOPT_CONNECTTIMEOUT, 3L); //连接超时时长
    curl_easy_setopt(pcurl, CURLOPT_HEADER, 0L);  //若启用，会将头文件的信息作为数据流输出
    curl_easy_setopt(pcurl, CURLOPT_WRITEFUNCTION, WriteMemoryCallback);  //得到请求结果后的回调函数
    curl_easy_setopt(pcurl, CURLOPT_POSTFIELDS, data.c_str());


    MemoryStruct data_trunk;  //请求结果的保存格式
    curl_easy_setopt(pcurl, CURLOPT_WRITEDATA, &data_trunk);

    curl_easy_setopt(pcurl, CURLOPT_NOSIGNAL, 1L); //关闭中断信号响应
    curl_easy_setopt(pcurl, CURLOPT_VERBOSE, 1L); //启用时会汇报所有的信息
    curl_easy_setopt(pcurl, CURLOPT_URL, url.c_str() ); //需要获取的URL地址

    std::string client_id_secret = HTTP_CLIENT_ID_SECRET;
    int client_len = client_id_secret.size();
    int base_out_len = (client_len/3 + 1 ) * 4 + 1;
    char *base_out = (char *)malloc(base_out_len);
    if (!base_out)
    {
        AK_LOG_WARN << "Memory alloc failed.size=" << base_out_len;
        return -2;
    }
    memset(base_out, 0, base_out_len);
    Base64Encode(client_id_secret.c_str(), client_len, base_out, &base_out_len);

    std::string auth = "Authorization:";
    auth += base_out;
    free(base_out);

    curl_slist *slist = NULL;
    //slist = curl_slist_append(slist,"Accept-Encoding:gzip, deflate, sdch");
    slist = curl_slist_append(slist,"Connection:keep-alive");
    slist = curl_slist_append(slist, auth.c_str());
    if(HTTP_DATA_TYPE_JSON == data_type)
    {
        slist = curl_slist_append(slist,"Content-Type:application/json");
    }
    else
    {
        slist = curl_slist_append(slist, "Content-Type:application/x-www-form-urlencoded");
    }

    for (const auto& header : headers)
    {
       slist = curl_slist_append(slist, header.c_str());
    }

    curl_easy_setopt(pcurl, CURLOPT_HTTPHEADER, slist);

    res = curl_easy_perform(pcurl);  //执行请求
    if (res != CURLE_OK)
    {
        const char *error_desc= curl_easy_strerror(res);
        AK_LOG_INFO << "Post Failed, res=" << res << ";error_desc=" << error_desc;
    }

    //正确响应后，请请求转写成本地文件的文件
    long res_code=0;
    res = curl_easy_getinfo(pcurl, CURLINFO_RESPONSE_CODE, &res_code);
    if(( res == CURLE_OK ) && (res_code == 200 || res_code == 201))
    {
        respone = data_trunk.memory;
        AK_LOG_INFO << "Post Success, reponse=" << respone;
        ret = 0;
    }
    else
    {
        AK_LOG_INFO << "Post Failed, res=" << res << ";res_code=" << res_code;
        SendHttpConnErrorAlarm(url);
    }

    curl_slist_free_all(slist);
    curl_easy_cleanup(pcurl);
    curl_global_cleanup();
    return ret;
}

int HttpRequest::GetWithHeaders(const std::string &urlhead, const std::vector<std::string> &headers,
                 const HttpRespuestKV &kv,  std::string &respone)
{
    int ret = -1;
    CURLcode res = curl_global_init(CURL_GLOBAL_ALL);
    if(CURLE_OK != res)
    {
        AK_LOG_WARN << "curl init failed";
        return -1;
    }

    CURL *pcurl = NULL;
    pcurl = curl_easy_init();
    if( NULL == pcurl)
    {
        AK_LOG_WARN << "Init CURL failed...";
        return -1;
    }

    std::stringstream url;
    url << urlhead << "?";
    for (const auto& tmpkv : kv)
    {
        url << "&" << tmpkv.first  << "=" << tmpkv.second;
    }

    AK_LOG_INFO << "http get:" << url.str();

    curl_easy_setopt(pcurl, CURLOPT_TIMEOUT, 5L);//请求超时时长
    curl_easy_setopt(pcurl, CURLOPT_CONNECTTIMEOUT, 3L); //连接超时时长
    curl_easy_setopt(pcurl, CURLOPT_SSL_VERIFYPEER, 0L); //去掉证书认证
    curl_easy_setopt(pcurl, CURLOPT_SSL_VERIFYHOST, false);
    curl_easy_setopt(pcurl, CURLOPT_HEADER, 0L);  //若启用，会将头文件的信息作为数据流输出
    curl_easy_setopt(pcurl, CURLOPT_WRITEFUNCTION, WriteMemoryCallback);  //得到请求结果后的回调函数

    MemoryStruct data_trunk;  //请求结果的保存格式
    curl_easy_setopt(pcurl, CURLOPT_WRITEDATA, &data_trunk);

    curl_easy_setopt(pcurl, CURLOPT_NOSIGNAL, 1L); //关闭中断信号响应
    curl_easy_setopt(pcurl, CURLOPT_VERBOSE, 1L); //启用时会汇报所有的信息
    curl_easy_setopt(pcurl, CURLOPT_URL, url.str().c_str() ); //需要获取的URL地址


    std::string client_id_secret = HTTP_CLIENT_ID_SECRET;
    int client_len = client_id_secret.size();
    int base_out_len = (client_len/3 + 1 ) * 4 + 1;
    char *base_out = (char *)malloc(base_out_len);
    if (!base_out)
    {
        AK_LOG_WARN << "Memory alloc failed.size=" << base_out_len;
        return -2;
    }
    memset(base_out, 0, base_out_len);
    Base64Encode(client_id_secret.c_str(), client_len, base_out, &base_out_len);

    std::string auth = "Authorization:";
    auth += base_out;
    free(base_out);

    curl_slist *slist = NULL;
    //slist = curl_slist_append(slist,"Accept-Encoding:gzip, deflate, sdch");
    slist = curl_slist_append(slist,"Connection:keep-alive");
    slist = curl_slist_append(slist, HTTP_HEAD_API_VERSION.c_str());
    slist = curl_slist_append(slist, auth.c_str());
    slist = curl_slist_append(slist, "Content-Type:application/x-www-form-urlencoded");

    for (const auto& header : headers)
    {
        slist = curl_slist_append(slist, header.c_str());
    }

    curl_easy_setopt(pcurl, CURLOPT_HTTPHEADER, slist);

    res = curl_easy_perform(pcurl);  //执行请求

    long res_code=0;
    res = curl_easy_getinfo(pcurl, CURLINFO_RESPONSE_CODE, &res_code);

    //正确响应后，请请求转写成本地文件的文件
    if(( res == CURLE_OK ) && (res_code == 200 || res_code == 201))
    {
        respone = data_trunk.memory;
        ret = 0;
    }

    curl_slist_free_all(slist);
    curl_easy_cleanup(pcurl);
    curl_global_cleanup();
    return ret;
}